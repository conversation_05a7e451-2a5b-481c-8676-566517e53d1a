{
  // Formatter settings - using built-in VSCode formatters
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features",
    "editor.formatOnSave": true
  },
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features",
    "editor.formatOnSave": true
  },
  "[scss]": {
    "editor.defaultFormatter": "vscode.css-language-features",
    "editor.formatOnSave": true
  },
  "[css]": {
    "editor.defaultFormatter": "vscode.css-language-features",
    "editor.formatOnSave": true
  },

  // Editor settings based on TSLint rules
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "editor.rulers": [140],
  "editor.wordWrap": "wordWrapColumn",
  "editor.wordWrapColumn": 140,

  // TypeScript/JavaScript formatting settings based on TSLint rules
  "typescript.preferences.quoteStyle": "single",
  "javascript.preferences.quoteStyle": "single",
  "typescript.format.semicolons": "insert",
  "javascript.format.semicolons": "insert",
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true,
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false,
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false,
  "typescript.format.insertSpaceAfterCommaDelimiter": true,
  "javascript.format.insertSpaceAfterCommaDelimiter": true,
  "typescript.format.insertSpaceBeforeAndAfterBinaryOperators": true,
  "javascript.format.insertSpaceBeforeAndAfterBinaryOperators": true,
  "typescript.format.insertSpaceAfterKeywordsInControlFlowStatements": true,
  "javascript.format.insertSpaceAfterKeywordsInControlFlowStatements": true,
  "typescript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": true,
  "javascript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": true,
  "typescript.format.placeOpenBraceOnNewLineForFunctions": false,
  "javascript.format.placeOpenBraceOnNewLineForFunctions": false,
  "typescript.format.placeOpenBraceOnNewLineForControlBlocks": false,
  "javascript.format.placeOpenBraceOnNewLineForControlBlocks": false,

  // Additional editor settings
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  },

  // Angular specific settings
  "emmet.includeLanguages": {
    "typescript": "html"
  },

  // File associations
  "files.associations": {
    "*.ts": "typescript"
  }
}
