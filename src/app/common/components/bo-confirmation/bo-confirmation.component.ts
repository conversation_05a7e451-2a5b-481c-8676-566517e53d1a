import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface TranslateParams {
  [key: string]: any;
}

export interface ConfirmationDialogData {
  message: string;
  params?: TranslateParams;
}

@Component({
    selector: 'bo-confirmation',
    templateUrl: './bo-confirmation.component.html',
    standalone: false
})

export class BoConfirmationComponent {

  constructor(public dialogRef: MatDialogRef<BoConfirmationComponent>,
              @Inject(MAT_DIALOG_DATA) public data: ConfirmationDialogData
  ) {
  }

  onNoClick() {
    this.dialogRef.close(false);
  }

  onConfirmClick() {
    this.dialogRef.close(true);
  }
}
