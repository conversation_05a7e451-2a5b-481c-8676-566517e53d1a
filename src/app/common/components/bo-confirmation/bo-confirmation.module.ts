import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';

import { LayoutModule } from '@angular/cdk/layout';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { BoConfirmationComponent } from './bo-confirmation.component';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    LayoutModule,
    MatDialogModule
  ],
  exports: [BoConfirmationComponent],
  declarations: [BoConfirmationComponent],
  providers: [],

})
export class BoConfirmationModule {
}
