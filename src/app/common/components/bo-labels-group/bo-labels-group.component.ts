import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { take } from 'rxjs/operators';
import { tagClassMap } from '../../../app.constants';
import { LabelsService } from '../../services/labels.service';
import { Label } from '../../typings';


@Component({
    selector: 'bo-labels-group',
    templateUrl: 'bo-labels-group.component.html',
    styleUrls: ['./bo-labels-group.component.scss'],
    standalone: false
})

export class BoLabelsGroupComponent implements OnInit {

  @ViewChild('textInput') textInput: ElementRef;
  @Input() dropdownHeight: any;
  @Input() isCreateNewLabelAllowed = true;
  @Input() isEditDisabled = false;
  @Input() groupId: string;

  @Output() select: EventEmitter<Label[]> = new EventEmitter();

  public searchText: string = null;
  public isEditable = false;
  public processedAvailableLabels = [];
  public processedSelectedLabels = [];
  public processedDropdownHeight = 'auto';
  public dropdownStatus: { isopen: boolean; } = { isopen: false };

  private availableLabels = new BehaviorSubject<Label[]>([]);
  private selectedLabels = new BehaviorSubject<Label[]>([]);
  private revertedSelectedArray: Label[];

  @Input()
  set labels(value: Label[]) {
    if (value) {
      this._labels = value;
      this.availableLabels.next(value);
    }
  }

  get labels(): Label[] {
    return this._labels;
  }

  @Input()
  set selected(value: Label[]) {
    if (value) {
      this.selectedLabels.next(value);
    }
  }

  private _labels: Label[];

  constructor(private elementRef: ElementRef,
    private labelsService: LabelsService,
  ) {
  }

  ngOnInit(): void {
    this.availableLabels.subscribe((labels) => {
      if (labels) {
        this.filterAvailables(labels);
      }
    });
    this.selectedLabels.subscribe((selected) => {
      if (selected) {
        this.processedSelectedLabels = selected;
      }
    });
    this.setDropdownHeight();
  }

  onEditClick() {
    if (this.isEditDisabled) return;

    if (this.isEditable === false) {
      this.createRevertSelectedArray();
    }
    this.setIsEditableTrue();
    this.setInputFocus();
  }

  onSelectBtnClick() {
    this.setInputFocus();
  }

  onSaveClick(dropdown) {
    this.select.emit(Array.from(this.processedSelectedLabels));
    this.clearSearch();
    dropdown.hide();
    this.setIsEditableFalse();
  }

  onCancelClick(dropdown) {
    this.setIsEditableFalse();
    this.clearSearch();
    this.revertSelected();
    this.filterAvailables(this.availableLabels.getValue());
    dropdown.hide();
  }

  onDropdownItemClick(label, event) {
    event.preventDefault();
    this.addToSelected(label);
    this.removeFromAvailable(label);
    this.clearSearch();
  }

  onAddClick(event) {
    event.preventDefault();
    this.createNewLabel();
    this.clearSearch();
  }

  onRemoveLabelClick(label, event) {
    event.preventDefault();
    this.removeFromSelected(label);
    this.filterAvailables(this.availableLabels.getValue());
  }

  handleDropdownItemKeydown(label: Label, event: KeyboardEvent, labelIndex): void {
    event.preventDefault();
    let dropdownItemArray = this.elementRef.nativeElement.querySelectorAll('.bo-labels .dropdown-item') as any;
    let goNext = function () {
      if (dropdownItemArray[labelIndex + 1]) {
        dropdownItemArray[labelIndex + 1].focus();
      } else {
        dropdownItemArray[0].focus();
      }
    };
    let goPrev = function () {
      if (dropdownItemArray[labelIndex - 1]) {
        dropdownItemArray[labelIndex - 1].focus();
      } else {
        dropdownItemArray[dropdownItemArray.length - 1].focus();
      }
    };
    if (event.key === 'Enter') {
      this.onDropdownItemClick(label, event);
      goNext();
    } else if (event.key === 'ArrowDown') {
      goNext();
    } else if (event.key === 'ArrowUp') {
      goPrev();
    }
  }

  handleInputKeydown(dropdown, event: KeyboardEvent) {
    dropdown.show();
    if (event.key === 'ArrowDown') {
      setTimeout(() => {
        let dropdownItem = this.elementRef.nativeElement.querySelector('.dropdown-item') as any;
        if (dropdownItem) {
          dropdownItem.focus();
        }
      }, 0);
    } else if (event.key === 'Enter') {
      event.preventDefault();
    }
  }

  handleAddItemKeydown(event) {
    if (event.key === 'Enter') {
      this.onAddClick(event);
      this.setInputFocus();
    }
  }

  filterAvailables(labels) {
    if (labels && this.processedSelectedLabels) {
      this.processedAvailableLabels = labels.filter(label => {
        return !this.processedSelectedLabels.find(el => el.title === label.title);
      });
    }
  }

  createRevertSelectedArray() {
    this.revertedSelectedArray = [...this.selectedLabels.getValue()];
  }

  revertSelected() {
    this.selected = [...this.revertedSelectedArray];
  }

  addToSelected(label) {
    if (label) {
      this.selectedLabels.next(this.selectedLabels.getValue().concat([label]));
    }
  }

  removeFromAvailable(label) {
    let availableLabelsArr = this.processedAvailableLabels;
    if (label) {
      let labelIndex = availableLabelsArr.indexOf(label);
      if (labelIndex > -1) {
        availableLabelsArr.splice(labelIndex, 1);
      }
    }
  }

  removeFromSelected(label) {
    let labelsArray = this.selectedLabels.getValue();
    let labelToRemove = labelsArray.find(x => x.title === label.title);
    let indexLabelToRemove = labelsArray.indexOf(labelToRemove);
    if (indexLabelToRemove !== -1) {
      labelsArray.splice(indexLabelToRemove, 1);
    }
  }

  createNewLabel() {
    this.labelsService.addLabel({ groupId: this.groupId, title: this.searchText })
      .pipe(
        take(1),
      )
      .subscribe(
        (label: Label) => {
          this.addToSelected({ id: label.id, title: label.title });
          this.labels = [...this.labels, label];
        }
      );
  }

  setIsEditableTrue() {
    this.isEditable = true;
  }

  setIsEditableFalse() {
    this.isEditable = false;
  }

  setInputFocus() {
    let onElement = this.elementRef.nativeElement.querySelector('#textInput') as any;
    setTimeout(() => onElement.focus(), 0);
  }

  getLabelClass(item: { group: string; }): string {
    return tagClassMap[item.group];
  }

  setDropdownHeight() {
    let value = this.dropdownHeight;
    if (typeof (value) === 'number') {
      this.processedDropdownHeight = value + 'px';
    }
  }

  changeDropdownStatus(value: boolean): void {
    this.dropdownStatus.isopen = value;
  }

  clearSearch() {
    this.searchText = '';
  }

  isNewBtnVisible(searchText: string): boolean {
    return this.isCreateNewLabelAllowed
      && searchText
      && !this.processedAvailableLabels.find(label => label.title === searchText)
      && !this.processedSelectedLabels.find(label => label.title === searchText);
  }

}
