import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
    name: 'boLabelsGroupFilter',
    standalone: false
})
export class BoLabelsGroupPipe implements PipeTransform {
  transform( items: any[], searchText: string ): any[] {
    if (!items ) return [];
    if (!searchText ) return items;
    searchText = searchText.toLowerCase();
    let filteredArr = items.filter( it => {
      return it.title.toLowerCase().includes(searchText);
    });
    if (filteredArr.length === 0){
      return [-1];
    }
    return filteredArr;
  }
}
