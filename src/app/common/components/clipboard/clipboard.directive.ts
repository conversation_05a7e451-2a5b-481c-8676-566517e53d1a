import { Directive, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import ClipboardJS from 'clipboard';


@Directive({
  selector: '[clipboard]',
  standalone: false
})
export class ClipboardDirective implements OnInit, OnDestroy {

  @Input() targetFn: Function;
  @Input() textFn: Function;
  @Input() container: Element;

  @Output() success: EventEmitter<any> = new EventEmitter();
  @Output() error: EventEmitter<any> = new EventEmitter();

  private clipboard: ClipboardJS;

  constructor(
    private element: ElementRef,
  ) {
  }

  ngOnInit() {
    const options = this.getOptions();

    this.clipboard = new ClipboardJS(this.element.nativeElement, options);
    this.clipboard.on('success', (e) => this.success.emit(e));
    this.clipboard.on('error', (e) => this.error.emit(e));
  }

  ngOnDestroy() {
    this.clipboard.destroy();
  }

  private getOptions() {
    const options = {};

    if (this.targetFn) {
      options['target'] = this.targetFn;
    }

    if (this.textFn) {
      options['text'] = this.textFn;
    }

    if (this.container) {
      options['container'] = this.container;
    }

    return options;
  }
}
