import { Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ValidationService } from '../../services/validation.service';

@Component({
    selector: 'control-messages',
    templateUrl: 'control-messages.component.html',
    standalone: false
})
export class ControlMessagesComponent {
  @Input() control: FormControl;
  @Input() forceShow: boolean;
  @Input('message') messageOverrides: { [key: string]: string };

  validationErrors: Object;

  constructor() {
  }

  get errorMessage() {
    for (let propertyName in this.control.errors) {
      if (this.control.errors.hasOwnProperty(propertyName) && (this.control.touched || this.forceShow)) {
        this.validationErrors = this.control.errors[propertyName];
        return ValidationService.getValidatorErrorMessage(
          propertyName,
          this.validationErrors,
          this.messageOverrides);
      }
    }

    return null;
  }
}
