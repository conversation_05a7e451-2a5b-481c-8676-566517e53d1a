import {
  Component,
  ElementRef,
  Input,
  Renderer2,
  ViewChild,
  forwardRef
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
    selector: 'editable',
    templateUrl: './editable.component.html',
    providers: [
        { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => EditableComponent), multi: true }
    ],
    styleUrls: [
        './editable.component.scss',
    ],
    standalone: false
})
export class EditableComponent implements ControlValueAccessor {

  @ViewChild('content', { static: true }) content: ElementRef;

  @Input() public placeholder: string;

  public value: string;

  private onChange: (value: string) => void;
  private onTouched: () => void;
  private removeDisabledState: () => void;
  private propValueAccessor: string = 'textContent';

  constructor(private renderer: Renderer2
  ) {
  }

  focusContentContainer() {
    this.content.nativeElement.focus();
  }

  callOnChange() {
    if (typeof this.onChange === 'function') {
      this.onChange(this.content.nativeElement[this.propValueAccessor]);
    }
  }

  callOnTouched() {
    if (typeof this.onTouched === 'function') {
      this.onTouched();
    }
  }

  writeValue(value: any): void {
    this.value = value === null ? '' : value;
    this.renderer.setProperty(this.content.nativeElement, this.propValueAccessor, this.value);

  }

  registerOnChange(fn: () => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    if (isDisabled) {
      this.renderer.setAttribute(this.content.nativeElement, 'disabled', 'true');
      this.removeDisabledState = this.renderer
        .listen(this.content.nativeElement, 'keydown', this.listenerDisabledState);
    } else {
      if (this.removeDisabledState) {
        this.renderer.removeAttribute(this.content.nativeElement, 'disabled');
        this.removeDisabledState();
      }
    }
  }

  private listenerDisabledState(e: KeyboardEvent) {
    e.preventDefault();
  }
}
