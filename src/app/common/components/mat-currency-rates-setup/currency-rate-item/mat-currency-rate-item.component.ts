import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Component({
    selector: 'mat-currency-rate-item',
    templateUrl: 'mat-currency-rate-item.component.html',
    styleUrls: ['./mat-currency-rate-item.component.scss'],
    standalone: false
})

export class MatCurrencyRateItemComponent implements OnInit {
  @Input() setupCurrency: string;
  @Input() currencies: string[];
  @Input() selected: string[];
  @Input() rateForm: FormGroup;

  @Output() rateRemove: EventEmitter<any> = new EventEmitter();

  constructor() {
  }

  ngOnInit() {
  }

  alreadySelected( curcode ): boolean {
    return this.selected.indexOf(curcode) > -1;
  }

  removeRate( event ) {
    event.preventDefault();
    this.rateRemove.emit(true);
  }
}
