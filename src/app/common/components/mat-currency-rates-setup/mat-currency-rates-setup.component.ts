import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { from, of, Subject } from 'rxjs';
import { mergeMap, takeUntil } from 'rxjs/operators';
import { VirtualCurrencyRateSettings } from '../../models/entity-settings.model';

export interface RateItemData {
  currency: string;
  value: number;
}

export interface RateCurrencyData {
  setupCurrency: string;
  rates: RateItemData[];
}

@Component({
    selector: 'mat-currency-rates-setup',
    templateUrl: 'mat-currency-rates-setup.component.html',
    styleUrls: [
        './mat-currency-rates-setup.component.scss',
    ],
    standalone: false
})

export class MatCurrencyRatesSetupComponent implements OnInit {

  @Input() currencies: string[] = [];
  @Input() setupCurrency: string;
  @Input() virtualCurrencyRate: VirtualCurrencyRateSettings;

  @Output() ratesChange: EventEmitter<any> = new EventEmitter();

  form: FormGroup;
  selected: string[] = [];

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
  ) {
  }

  ngOnInit() {
    this.initForm();
    this.initOutput();
    this.populateForm();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  addRate( event ) {
    event.preventDefault();
    this.ratesFormArray.push(this.getRateForm());
  }

  removeRate( rateForm: FormGroup ) {
    const idx = this.ratesFormArray.controls.indexOf(rateForm);
    if (this.ratesFormArray.controls.length > 1) {
      this.removeFromSelected(rateForm.get('currency').value);
      this.ratesFormArray.removeAt(idx);
    }
  }

  get ratesControls(): FormGroup[] {
    return this.ratesFormArray.controls as FormGroup[];
  }

  private get ratesFormArray(): FormArray {
    return this.form.get('rates') as FormArray;
  }

  private initForm() {
    this.form = this.fb.group({
      setupCurrency: [this.setupCurrency, Validators.required],
      rates: this.fb.array([
        this.getRateForm()
      ])
    });
  }

  private getRateForm( data: RateItemData = {currency: '', value: undefined} ): FormGroup {
    const rateForm = this.fb.group({
      currency: [data.currency, Validators.required],
      value: [data.value, this.getRateValueValidators()]
    });

    rateForm.get('currency').valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( next ) => {
      const prev = rateForm.value.currency;
      this.removeFromSelected(prev);
      this.selected.push(next);
    });

    return rateForm;
  }

  private removeFromSelected( currency ) {
    const idx = this.selected.indexOf(currency);

    if (idx > -1) {
      this.selected.splice(idx, 1);
    }
  }

  private getRateValueValidators(): ValidatorFn {
    return Validators.compose([
      Validators.required,
      Validators.min(0.0001)
    ]);
  }

  private initOutput() {
    this.form.valueChanges.subscribe(( value ) => {
      const data = {
        [value.setupCurrency]: value.rates.reduce(this.reduceToCurrencyHash, {})
      };

      this.ratesChange.emit(data);
    });
  }

  private populateForm() {
    if (this.virtualCurrencyRate) {
      const setupCurrencyData = this.expandCurrencyHash(this.virtualCurrencyRate)
        .find((item: RateCurrencyData) => item.setupCurrency === this.setupCurrency);

      if (setupCurrencyData && setupCurrencyData.rates) {
        this.clearRatesFormArray();

        of(setupCurrencyData)
          .pipe(
            mergeMap((data) => {
              data.rates = data.rates.filter(item => this.rateCurrencyExist(item));
              this.form.patchValue(data);
              return from(data.rates);
            })
          )
          .subscribe((item: RateItemData) => {
            this.selected.push(item.currency);
            this.ratesFormArray.push(this.getRateForm(item));
          });
      }
    }
  }

  private rateCurrencyExist(rateItemData: RateItemData): boolean {
    const rateCurrencyExist = this.currencies.indexOf(rateItemData.currency) > -1;
    const rateCurrencyAdding = rateItemData.currency === '';
    return rateCurrencyExist || rateCurrencyAdding;
  }


  private reduceToCurrencyHash( hash, rate ) {
    let item = {};
    if ('currency' in rate && rate.currency !== '' && rate.value !== '') {
      item = { [rate.currency]: rate.value };
    }
    return { ...hash, ...item };
  }

  private expandCurrencyHash( hash: VirtualCurrencyRateSettings ): RateCurrencyData[] {
    return Object.keys(hash).map((setupCurrency) => {
      return <RateCurrencyData>{
        setupCurrency: setupCurrency,
        rates: Object.keys(hash[setupCurrency])
          .map(( currency ) => {
            return <RateItemData>{ currency: currency, value: hash[setupCurrency][currency] };
          })
      };
    });
  }

  private clearRatesFormArray() {
    while (this.ratesFormArray.controls.length) {
      this.ratesFormArray.removeAt(0);
    }
  }
}
