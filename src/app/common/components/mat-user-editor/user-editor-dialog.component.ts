import { Component, Inject, OnDestroy, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable, Subject } from 'rxjs';
import { mergeMap, switchMap, takeUntil, tap } from 'rxjs/operators';
import { PERMISSIONS_NAMES } from '../../../app.constants';
import { User } from '../../../pages/users/user.model';
import { Entity } from '../../models/entity.model';
import { EntityService } from '../../services/entity.service';
import { UserService } from '../../services/user.service';
import { UserFormComponent } from './user-form.component';

export interface UserEditorDialogData {
  user: User;
  entity: Entity;
  brief: Entity;
}

@Component({
    templateUrl: 'user-editor-dialog.component.html',
    standalone: false
})
export class UserEditorDialogComponent implements OnDestroy {

  @ViewChild(UserFormComponent) userForm: UserFormComponent;

  submittedData: User;
  entityInfo: Entity;
  isEdit: boolean;
  excludedEmails: string[] = [];
  excludedPasswords: string[] = [];
  loading: boolean = false;
  readonly isSuperAdmin: boolean;

  backendErrorMessages: { [validation: string]: string } = {};

  private readonly destroyed$ = new Subject<void>();

  constructor(
    public dialogRef: MatDialogRef<UserEditorDialogComponent, User>,
    @Inject(MAT_DIALOG_DATA) public data: UserEditorDialogData,
    private userService: UserService<User>,
    private notifications: SwuiNotificationsService,
    private authService: SwHubAuthService,
    private translate: TranslateService,
    private entityService: EntityService<Entity>,
  ) {
    this.isSuperAdmin = this.authService.isSuperAdmin;
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onFormSubmitted( { isEdit, data } ) {
    const sources: Observable<any>[] = [];
    let saveUser$;
    this.loading = true;
    let passwordChanging = false;

    if (isEdit) {
      const updateData = { ...data };

      if ('password' in data) {
        if (this.hasForcePasswordPermissions() && data.password !== '') {
          sources.push(this.userService.forceResetUserPassword(data));
          passwordChanging = true;
        }
        delete updateData.password;
      }

      if ('email' in data) {
        if (this.hasForceEmailPermissions() && this.data.user.email !== data.email) {
          sources.push(this.userService.forceSetUserEmail(data));
        }
        delete updateData.email;
      }

      if ('userType' in data) {
        if (this.hasUserTypePermissions() && this.data.user.userType !== data.userType) {
          sources.push(this.userService.setUserType(data));
        }

        delete updateData.userType;
      }

      if (updateData.username !== this.data.user.username) {
        saveUser$ = this.userService.updateItem(updateData, this.data.user.username);
      } else {
        saveUser$ = this.userService.updateItem(updateData);
      }

    } else {
      if (data.entity !== '') {
        sources.push(this.entityService.getItem(data.entity).pipe(
          tap(entityInfo => {
            this.entityInfo = entityInfo;
          })
        ));
      } else {
        this.entityInfo = this.data.brief;
      }

      saveUser$ = this.userService.createItem(data, data.entity);
    }

    const save$ = sources.reduce(( merged$, current$ ) => {
      return merged$.pipe(mergeMap(() => current$));
    }, saveUser$);

    save$
      .pipe(
        switchMap(() => {
          let code = 'ENTITY_SETUP.REGIONAL.MODALS.notificationUserAdded';
          if (isEdit) {
            code = 'ENTITY_SETUP.REGIONAL.MODALS.notificationUserUpdated';
          }
          return this.translate.get(code, { name: data.username });
        }),
        takeUntil(this.destroyed$)
      )
      .subscribe(
        ( message ) => {
          this.notifications.success(message, '');
          if (passwordChanging) {
            this.notifications.success('Password has been changed successfully');
          }
          // this.userModified.emit(data);
          this.submittedData = data;
          this.isEdit = isEdit;
          this.loading = false;

          if (this.isEdit) {
            this.dialogRef.close(data);
          }
        },
        ( error ) => this.handleBackendValidation(error, data),
      );
  }

  saveChanges( $event: Event ) {
    $event.preventDefault();
    if (this.userForm) {
      this.userForm.onFormSubmitFn($event);
    }
  }

  private hasUserTypePermissions(): boolean {
    let permission: string = PERMISSIONS_NAMES.USER_CHANGE_TYPE;

    if (this.data.user.entity === '') {
      permission = PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_TYPE;
    }

    return this.authService.allowedTo([permission]);
  }

  private hasForcePasswordPermissions(): boolean {
    let permission: string = PERMISSIONS_NAMES.FORCE_RESET_PASSWORD;

    if (this.data.user.entity === '') {
      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_RESET_PASSWORD;
    }

    return this.authService.allowedTo([permission]);
  }

  private hasForceEmailPermissions(): boolean {
    let permission: string = PERMISSIONS_NAMES.FORCE_SET_EMAIL;

    if (this.data.user.entity === '') {
      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_SET_EMAIL;
    }

    return this.authService.allowedTo([permission]);
  }

  private handleBackendValidation( errorResponse, data ) {
    const email = data['email'];
    const password = data['password'];
    const err = errorResponse.error;

    if ('code' in err) {

      switch (err.code) {
        case 199:
          if (email) {
            this.excludedEmails = [...this.excludedEmails, email];
          }
          break;

        case 40:
        case 228:
          if (password) {
            this.excludedPasswords = [...this.excludedPasswords, password];
          }
          this.backendErrorMessages['notEqualsPassword'] = err.message;
          break;

        default:
          break;
      }
    }

    this.loading = false;
  }
}
