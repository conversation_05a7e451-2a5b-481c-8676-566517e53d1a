import { Component, EventEmitter, Input, <PERSON><PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SelectOptionItem, SwHubAuthService, SwuiSelectOption } from '@skywind-group/lib-swui';
import { BehaviorSubject, Subject } from 'rxjs';
import { distinctUntilChanged, filter, finalize, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { PERMISSIONS_NAMES } from '../../../app.constants';
import {
  USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST, USER_STATUS_LIST, USER_TYPES_MAP
} from '../../../pages/business-management/components/entities/tab-users/users.schema';
import { Role } from '../../../pages/users/components/roles/role.model';
import { User } from '../../../pages/users/user.model';
import { Entity } from '../../models/entity.model';
import { SelectOptionModel } from '../../models/select-option.model';
import { entitiesStructureToSelectOptions, EntityService } from '../../services/entity.service';
import { ValidationService } from '../../services/validation.service';
import { BoConfirmationComponent } from '../bo-confirmation/bo-confirmation.component';
import { UserEditorService } from './user-editor.service';


export interface ErrorMessage {
  [key: string]: any;
}

@Component({
    selector: 'user-form',
    templateUrl: './user-form.component.html',
    styleUrls: ['./user-form.component.scss'],
    standalone: false
})
export class UserFormComponent implements OnInit, OnDestroy {
  @Input() user: User = new User();
  @Input() brief?: Entity;
  @Input() entity?: Entity;
  @Input() backendErrorMessages: { [validation: string]: string; } = {};
  userStatus?: string;

  @Output() formSubmitted = new EventEmitter<any>();

  readonly statuses = USER_STATUS_LIST;
  readonly userTypes = USER_TYPES_MAP;
  readonly changePasswordPeriodTypes = USER_CHANGE_PASSWORD_PERIOD_TYPE_LIST;
  readonly form: FormGroup;
  submitted = false;
  editMode = false;

  entityOptions: SelectOptionItem[] = [];
  entityOptionsLoading$ = new BehaviorSubject(false);

  roleSelectOptions: SelectOptionModel[] = [];

  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
    minLength: `VALIDATION.minLength`,
    invalidPassword: 'VALIDATION.invalidPassword',
    passwordMinLength: 'VALIDATION.passwordMinLength',
    notEqualsPassword: 'VALIDATION.notEqualsPassword',
    invalidEmailAddress: 'VALIDATION.invalidEmailAddress',
    invalidPhoneNumberMask: 'VALIDATION.invalidPhoneNumberMask',
    passwordContainLowercase: `VALIDATION.passwordContainLowercase`,
    passwordContainUppercase: `VALIDATION.passwordContainUppercase`,
    invalidLatinCharsDigitsSymbols: `VALIDATION.invalidLatinCharsDigitsSymbols`,
    min: `VALIDATION.min`,
  };
  unsharedRoles: SwuiSelectOption[];

  private _roles: Role[];
  private _selectedRoles: Role[];
  private _excludedEmails: string[];
  private _excludedPasswords: string[];
  private readonly destroyed$ = new Subject<any>();
  private allRoles: Role[];
  private userRoles: Role[];

  @Input() set roles(values: Role[]) {
    if (!Array.isArray(values)) return;
    this._roles = values;
    this.roleSelectOptions = values.map(role => new Role(role)).map(role => role.toSelectOption()).sort((a, b) => {
      return a.text.localeCompare(b.text);
    });
  }

  get roles(): Role[] {
    return this._roles;
  }

  @Input()
  set excludedEmails(emails: string[]) {
    if (!Array.isArray(emails)) return;
    this._excludedEmails = emails;
    this.updateEmailValidators();
  }

  get excludedEmails(): string[] {
    return this._excludedEmails;
  }

  @Input()
  set excludedPasswords(passwords: string[]) {
    if (!Array.isArray(passwords)) return;
    this._excludedPasswords = passwords;
    this.updatePasswordValidators();
  }

  get excludedPasswords(): string[] {
    return this._excludedPasswords;
  }

  @Input()
  set selectedRoles(value: Role[]) {
    if (!value) {
      return;
    }

    value.sort((a, b) => {
      return a.title.localeCompare(b.title);
    });

    if (Array.isArray(value) && value.length) {
      this.rolesControl.patchValue(value.map(item => item.id));
    }

    this._selectedRoles = value;
  }

  get selectedRoles(): Role[] {
    return this._selectedRoles;
  }

  constructor(private fb: FormBuilder,
    private authService: SwHubAuthService,
    private entityService: EntityService<Entity>,
    private dialog: MatDialog,
    private service: UserEditorService,
  ) {
    this.form = this.initForm();
  }

  ngOnInit() {
    this.editMode = !!this.user?.createdAt;
    this.buildEntityOptions();
    this.populateForm();
    this.onUserTypeChanged();
    this.subscribeToRoles();

    this.entityControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.rolesControl.setValue([]);
        this.updateRoles();
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get entityControl(): FormControl {
    return this.form.get('entity') as FormControl;
  }

  get firstNameControl(): FormControl {
    return this.form.get('firstName') as FormControl;
  }

  get lastNameControl(): FormControl {
    return this.form.get('lastName') as FormControl;
  }

  get usernameControl(): FormControl {
    return this.form.get('username') as FormControl;
  }

  get passwordControl(): FormControl {
    return this.form.get('password') as FormControl;
  }

  get emailControl(): FormControl {
    return this.form.get('email') as FormControl;
  }

  get phoneControl(): FormControl {
    return this.form.get('phone') as FormControl;
  }

  get rolesControl(): FormControl {
    return this.form.get('roles') as FormControl;
  }

  get statusControl(): FormControl {
    return this.form.get('status') as FormControl;
  }

  get userTypeControl(): FormControl {
    return this.form.get('userType') as FormControl;
  }

  onFormSubmitFn(event) {
    event.preventDefault();
    this.form.markAllAsTouched();
    this.submitted = true;
    if (this.form.valid) {
      if (this.editMode && this.userTypeControl.value === this.userTypes.operatorApi && this.passwordControl.value) {
        this.dialog.open(BoConfirmationComponent, {
          width: '500px',
          data: { message: 'ENTITY_SETUP.USERS.changePasswordNote' },
          disableClose: true
        }).afterClosed().pipe(
          filter(result => result),
          takeUntil(this.destroyed$)
        ).subscribe(() => {
          this.onFormSubmitConfirmFn();
        });
      } else {
        this.onFormSubmitConfirmFn();
      }
    }
  }

  onFormSubmitConfirmFn() {
    const value = this.form.value;
    if (value.entity === ':') {
      value.entity = '';
    }
    const data = this.editMode ? { ...this.user, ...value } : value;

    if ('roles' in data) {
      data.roles = this._roles.filter(({ id }) => data.roles.includes(id));
      delete data['role'];
    }

    this.formSubmitted.emit({ isEdit: this.editMode, data });
  }

  private initForm(): FormGroup {
    return this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      username: [
        '', [
          Validators.required,
          ValidationService.latinCharsDigitsSymbols,
          ValidationService.minLength(4)
        ]
      ],
      email: ['', this.getEmailValidators()],
      phone: ['', ValidationService.IfNotEmpty(ValidationService.phoneNumberValidator)],
      password: ['', this.getPasswordValidators()],
      status: [''],
      roles: [[], Validators.required],
      entity: [null, Validators.required],
      userType: ['', Validators.required],
      forcePasswordChangePeriodType: ['monthly', Validators.required],
      forcePasswordChangePeriod: [3, Validators.compose([Validators.required, Validators.min(1)])]
    });
  }

  private subscribeToRoles() {
    this.service.fetchAvailableRoles()
      .pipe(takeUntil(this.destroyed$))
      .subscribe(([roles, rolesHash, currentUser]) => {
        const selectedRoles = [];
        const unsharedRoles = [];

        this.user.roles.forEach((role) => {
          if (rolesHash[role.id]) {
            selectedRoles.push(role);
          } else {
            unsharedRoles.push({ id: role.id, text: role.title });
          }
        });

        this.selectedRoles = selectedRoles;

        if (this.selectedRoles) {
          this.rolesControl.patchValue(this.selectedRoles.map(item => item.id));
        }

        this.unsharedRoles = unsharedRoles;

        // add currentUser roles to sharedRoles
        this.allRoles = roles;
        this.userRoles = [...this.selectedRoles].map(role => {
          return Object.assign({}, role, { disabled: true });
        })
          .concat(currentUser.roles)
          .filter((value, pos, arr) => arr.findIndex(i => i.id === value.id) === pos);

        this.updateRoles();
      });
  }

  private updateRoles() {
    if (!this.entityControl.value) {
      this.roles = [];
      return;
    }
    const entity = this.entityOptions[this.entityControl.value];

    if (this.service.hasCreateEditRoleAccess(entity?.isRoot())) {
      this.roles = this.allRoles;
    } else {
      this.roles = this.userRoles;
    }
  }

  private onUserTypeChanged() {
    const permission = this.user.entity === '' ? PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_TYPE : PERMISSIONS_NAMES.USER_CHANGE_TYPE;

    if (this.editMode && !this.authService.areGranted([permission])) {
      this.form.get('forcePasswordChangePeriodType').disable();
      this.form.get('forcePasswordChangePeriod').disable();
      this.userTypeControl.disable();
    }

    this.userTypeControl.valueChanges.pipe(
      filter((userType) => this.editMode && userType === this.userTypes.bo),
      switchMap(() =>
        this.dialog.open(BoConfirmationComponent, {
          width: '500px',
          data: { message: 'ENTITY_SETUP.USERS.changeUserTypeNote' },
          disableClose: true
        }).afterClosed()),
      filter(result => !result),
      tap(() => this.userTypeControl.setValue(this.userTypes.operatorApi)),
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  private hasForcePasswordPermissions(): boolean {
    let permission: string = PERMISSIONS_NAMES.FORCE_RESET_PASSWORD;

    if (this.entity.isRoot()) {
      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_RESET_PASSWORD as string;
    }

    return this.authService.allowedTo([permission]);
  }

  private hasForceEmailPermissions(): boolean {
    let permission: string = PERMISSIONS_NAMES.FORCE_SET_EMAIL;

    if (this.entity.isRoot()) {
      permission = PERMISSIONS_NAMES.KEYENTITY_FORCE_SET_EMAIL as string;
    }

    return this.authService.allowedTo([permission]);
  }

  private updateEmailValidators() {
    this.emailControl.setValidators(this.getEmailValidators());
    this.emailControl.updateValueAndValidity();
  }

  private updatePasswordValidators() {
    const optional = this.user && 'createdAt' in this.user;

    this.passwordControl.setValidators(this.getPasswordValidators(optional));
    this.passwordControl.updateValueAndValidity();
  }

  private getEmailValidators() {
    let validators: ValidatorFn[] = [Validators.required, ValidationService.emailValidator];

    if (this.excludedEmails && this.excludedEmails.length) {
      validators = [
        ...validators,
        ...this.excludedEmails.map((email) => ValidationService.notEqualsString(email))
      ];
    }

    return Validators.compose(validators);
  }

  private getPasswordValidators(optional: boolean = false) {
    let validators: ValidatorFn[] = [
      ValidationService.latinCharsDigitsSymbols,
      ValidationService.passwordConditions(),
      ValidationService.passwordValidator,
    ];

    if (this.excludedPasswords && this.excludedPasswords.length) {
      validators = [
        ...validators,
        ...this.excludedPasswords.map((pwd) => ValidationService.notEqualsPassword(pwd))
      ];
    }

    if (this.user && 'username' in this.user) {
      validators.push(ValidationService.notEqualsString(this.user.username));
    }

    if (!optional) {
      validators.push(Validators.required);
    }

    return optional
      ? ValidationService.IfNotEmpty(Validators.compose(validators))
      : Validators.compose(validators);
  }

  private populateForm() {
    this.form.patchValue(this.user);

    if (this.editMode) {
      if (this.user.entity === '') {
        this.entityControl.setValue(':');

        if (!this.authService.isSuperAdmin) {
          this.form.disable({ emitEvent: false });
        }
      }

      this.passwordControl.disable();
      this.emailControl.disable();
      this.entityControl.disable();

      if (this.selectedRoles) {
        this.rolesControl.patchValue(this.selectedRoles.map(item => item.id));
      }

      this.statusControl.patchValue(this.user.status);
      this.userStatus = this.statusControl.value;
    }

    if (this.hasForcePasswordPermissions()) {
      this.passwordControl.enable();
      this.emailControl.setValidators(this.getEmailValidators());
    }

    if (this.hasForceEmailPermissions()) {
      this.emailControl.enable();
    }
  }

  private buildEntityOptions() {
    this.entityOptionsLoading$.next(true);
    this.entityService.getShortStructure().pipe(
      map(structure => entitiesStructureToSelectOptions(structure, 0, [], false)),
      map(entityOptions => entityOptions.map(option => ({
        ...option,
        id: option.id || ':'
      }))),
      map(entityOptions => {
        if (this.entity.path && this.entity.path !== ':') {
          return entityOptions.filter(({ id }) => id.indexOf(this.entity.path) === 0);
        }
        return entityOptions;
      }),
      map((entityOptions: SelectOptionItem[]) => {
        if (!this.authService.isSuperAdmin) {
          const rootOption = entityOptions.find(({ id }) => id === ':');
          if (rootOption) {
            rootOption.disabled = true;
          }
          const selfOption = entityOptions.find(({ id }) => id === this.brief.path);
          if (selfOption) {
            selfOption.disabled = true;
          }
        }
        return entityOptions;
      }),
      finalize(() => {
        this.entityOptionsLoading$.next(false);
      }),
      takeUntil(this.destroyed$)
    ).subscribe(entityOptions => {
      this.entityOptions = entityOptions;
    });
  }
}
