import { Component, EventEmitter, Inject } from '@angular/core';
import { SWUI_GRID_WIDGET_CONFIG, SwuiGridWidgetConfig } from '@skywind-group/lib-swui';

export interface ActionListTdWidgetSchema {
  td?: {
    arrayKey?: string;
    valueFn?: ( row: any, schema: { [name: string]: any } ) => any;
    isDisabled?: ( row: any, schema: { [name: string]: any } ) => any;
    onClick?: ( id: string ) => any;
  };
}

@Component({
    templateUrl: './action-list.widget.html',
    styleUrls: ['./action-list.widget.scss'],
    standalone: false
})
export class TdActionListWidget {
  value: any;
  disabled = false;
  private readonly action: EventEmitter<any>;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) private config: SwuiGridWidgetConfig<ActionListTdWidgetSchema> ) {
    const { schema, row, value, action } = config;
    this.value = (schema.td?.valueFn && schema.td.valueFn(row, schema)) || value;
    const key: any = schema.td?.arrayKey ? schema.td.arrayKey : 'id';

    this.value = this.value.reduce(( acc: any[], curr: { [key: string]: any; } ) => {
      if (curr && typeof curr === 'object') {
        if (curr[key]) {
          acc.push(curr[key]);
        }
      } else {
        acc.push(curr);
      }
      return acc;
    }, []);

    this.action = action;

    if (schema.td?.isDisabled) {
      this.disabled = schema.td.isDisabled(row, schema);
    }
  }

  onClick( index: number ) {
    if (this.action) {
      const { field, row } = this.config;
      this.action.emit({ row, field, index });
    }
  }
}
