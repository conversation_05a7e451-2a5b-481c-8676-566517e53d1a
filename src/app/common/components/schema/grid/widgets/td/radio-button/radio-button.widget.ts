import { Component, EventEmitter, Inject } from '@angular/core';
import { SWUI_GRID_WIDGET_CONFIG, SwuiGridWidgetConfig } from '@skywind-group/lib-swui';

@Component({
    templateUrl: './radio-button.widget.html',
    standalone: false
})
export class TDRadioButtonWidget {

  value: boolean;

  private readonly action: EventEmitter<any>;

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) private config: SwuiGridWidgetConfig<any>,
  ) {
    const { row, schema, action } = config;

    this.action = action;

    const valueFn = schema.td?.valueFn;
    this.value = schema.td && 'valueFn' in schema.td ? (valueFn && valueFn(row, schema)) : false;
  }

  onClicked( event: MouseEvent ) {
    if (this.action) {
      const { row, field } = this.config;
      this.action.emit({ eventType: 'radio', event, row, field });
    }
  }
}
