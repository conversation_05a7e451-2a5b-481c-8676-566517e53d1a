import { Component, ElementRef, Input, AfterViewInit, AfterViewChecked, ViewChild } from '@angular/core';
import * as Highcharts from 'highcharts';

import { Options } from 'highcharts';


export const HIGHCHARTS_TYPES = {
  area: 'area',
  arearange: 'arearange',
  areaspline: 'areaspline',
  areasplinerange: 'areasplinerange',
  bar: 'bar',
  boxplot: 'boxplot',
  bubble: 'bubble',
  column: 'column',
  columnrange: 'columnrange',
  errorbar: 'errorbar',
  funnel: 'funnel',
  gauge: 'gauge',
  heatmap: 'heatmap',
  line: 'line',
  pie: 'pie',
  polygon: 'polygon',
  pyramid: 'pyramid',
  scatter: 'scatter',
  solidgauge: 'solidgauge',
  spline: 'spline',
  treemap: 'treemap',
  waterfall: 'waterfall'
};

export interface ChartSchemaField {
  configField: string;
  configValue: Object;
}

export interface ChartDataField {
  data: number[];
}

@Component({
    selector: 'sw-highcharts',
    templateUrl: './highcharts.component.html',
    standalone: false
})
export class HighchartsComponent implements AfterViewInit, AfterViewChecked {
  @Input() schema: ChartSchemaField[];
  @Input() title: string;

  @ViewChild('chartContainer', { static: true }) protected chartContainer: ElementRef;

  protected config: Options = {};
  protected chart: any;
  private _data: ChartDataField[];

  @Input()
  set data( value: ChartDataField[] ) {
    this._data = value;
    this.applyDataToChart();
  }


  ngOnInit() {
  }

  ngAfterViewInit() {
    this.buildConfigBase();
    this.buildTooltip();
    this.renderChart();
  }

  ngAfterViewChecked() {
  }

  private getConfigPart( configFieldName: string ): Object {
    let { configField, configValue } = this.schema.find(field => field.configField === configFieldName)
    || { configField: configFieldName, configValue: {} };

    return { [configField]: configValue };
  }

  private buildConfigBase() {
    this.config = Object.assign({},
      this.getConfigPart('title'),
      this.getConfigPart('chart'),
      this.getConfigPart('xAxis'),
      this.getConfigPart('yAxis'),
      this.getConfigPart('plotOptions'),
      this.getConfigPart('series'),
      this.getConfigPart('legend'),
    );
  }

  private buildTooltip() {
    this.config = Object.assign({}, this.config, this.getConfigPart('tooltip'));
  }

  private applyDataToConfig() {
    if (this._data && this._data.length) {
      this._data.forEach(( obj: ChartDataField, index ) => {

        if (this.config.hasOwnProperty('series')) {
          this.config.series[index] = Object.assign({}, this.config.series[index], {
            data: obj.data
          });
        }
      });
    }
  }

  private applyDataToChart() {
    if (this.chart) {
      this._data.forEach(( obj: ChartDataField, index ) => {
        this.chart.series[index].setData(obj.data);
      });
      this.hideLoading();
    }
  }

  private showLoading() {
    if (!this._data || this._data.length === 0) {
      this.chart.showLoading();
    }
  }

  private hideLoading() {
    if (this.chart) {
      this.chart.hideLoading();
    }
  }

  private renderChart() {
    this.applyDataToConfig();
    this.chart = Highcharts.chart(this.chartContainer.nativeElement, this.config);
    this.showLoading();
  }

}
