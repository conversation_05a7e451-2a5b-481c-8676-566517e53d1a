import { Directive, HostBinding, Input } from '@angular/core';
import { WizardComponent } from './wizard.component';

export interface WizardStepDirectiveInterface {
  heading: string;
  id: string;
  disabled: boolean;
  classList: string;
  rendered: boolean;
  current: boolean;
  readonly hidden: boolean;
  readonly first: boolean;
  readonly last: boolean;
  readonly index: number;
  readonly stepNumber: string;
  readonly class: Object;
}

@Directive({
    selector: 'sw-wizard-step,[sw-wizard-step]',
    exportAs: 'wizard-step',
    standalone: false
})
export class WizardStepDirective {

  @Input() public heading: string;
  @Input() public id: string;
  @Input() public disabled: boolean;

  @HostBinding('class') @Input('class') classList: string = '';

  @HostBinding('class.body') rendered: boolean = true;

  protected _current: boolean;

  @HostBinding('class.current')
  @Input()
  public set current( current: boolean ) {
    this._current = current;

    if (!current) {
      return;
    }

    if (this.disabled) {
      this.disabled = false;
    }

    this.wizard.steps.forEach(( step: WizardStepDirective ) => {
      if (step !== this) {
        step.current = false;
      }
    });

  }

  public get current(): boolean {
    return this._current;
  }

  @HostBinding('class.hidden')
  public get hidden(): boolean {
    return !this.current;
  }

  public get first(): boolean {
    return this.index === 0;
  }

  public get last(): boolean {
    return this.index + 1 === this.wizard.steps.length;
  }

  public get index(): number {
    return this.wizard.steps.indexOf(this);
  }

  public get stepNumber(): string {
    return (this.index + 1).toString();
  }

  public get class(): Object {
    return {
      'current': this.current,
      'first': this.first,
      'last': this.last,
      'disabled': this.disabled,
      'done': !this.disabled && !this.current,
    };
  }

  constructor( public wizard: WizardComponent ) {
    this.wizard.addStep(this);
  }

}
