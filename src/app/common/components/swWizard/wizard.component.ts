import { Component, EventEmitter, Output } from '@angular/core';
import { WizardStepDirective, WizardStepDirectiveInterface } from './wizard-step.directive';

export const WIZARD_FORM_CONSTS = {
  label_prev: 'COMPONENTS.WIZARD.btnPrev',
  label_next: 'COMPONENTS.WIZARD.btnNext',
  label_submit: 'COMPONENTS.WIZARD.btnSubmit'
};

export class StepDetails {
  id: string;
  heading?: string;
  index: number;
  first: boolean;
  last: boolean;

  constructor( { id, heading, index, first, last }: WizardStepDirectiveInterface ) {
    Object.assign(this, { id, heading, index, first, last });
  }
}

export class StepChange {
  after: StepDetails;
  before: StepDetails;
}

export const WIZARD_DIRECTIONS = {
  next: 1,
  previous: -1,
};

@Component({
    selector: 'sw-wizard',
    templateUrl: './wizard.component.html',
    exportAs: 'wizard',
    standalone: false
})
export class WizardComponent {
  public steps: WizardStepDirective[] = [];
  public currentStep: WizardStepDirective;
  public currentStepIndex: number;

  @Output() public onNextStep: EventEmitter<StepChange> = new EventEmitter();
  @Output() public onPreviousStep: EventEmitter<StepChange> = new EventEmitter();
  @Output() public onStepChange: EventEmitter<StepChange> = new EventEmitter();
  @Output() public onComplete: EventEmitter<StepDetails> = new EventEmitter();

  constructor() {
  }

  public addStep( step: WizardStepDirective ) {
    this.steps.push(step);
    if (this.steps.length === 1) {
      this.setCurrentStep(step);
    }
    step.disabled = this.steps.length > 1;
  }

  public hasPreviousStep(): boolean {
    return this.currentStepIndex > 0;
  }

  public hasNextStep(): boolean {
    return this.currentStepIndex + 1 < this.steps.length;
  }

  public setPreviousStep( ) {
    if (this.currentStep.first) {
      return;
    }

    this._setNewStep(WIZARD_DIRECTIONS.previous);
  }

  public setNextStep( ) {
    if (this.currentStep.last) {
      this._completeWizard();
      return;
    }

    this._setNewStep(WIZARD_DIRECTIONS.next);
  }

  public setCustomStep( stepId: string ) {
    let requiredStep = this.steps.find(step => step.id === stepId);
    if (requiredStep) {
      this.setCurrentStep(requiredStep);
    }
  }

  public setFirstStep() {
    let first = this.currentStepIndex * -1;
    this._setNewStep(first);
  }

  private _completeWizard() {
    this.onComplete.emit(new StepDetails(this.currentStep));
  }

  private _setNewStep( direction: number = WIZARD_DIRECTIONS.next ) {
    let newStepIndex = this.currentStepIndex + direction;
    let noStep: boolean = typeof this.steps[newStepIndex] === 'undefined';

    if (noStep || !direction) {
      return;
    }

    this.steps.forEach(( step ) => {
      if (step.index !== newStepIndex) return;

      let change = <StepChange>{
        before: new StepDetails(this.currentStep),
        after: new StepDetails(step)
      };

      this.setCurrentStep(step);

      switch (direction) {
        case WIZARD_DIRECTIONS.next:
          this.onNextStep.emit(change);
          break;

        case WIZARD_DIRECTIONS.previous:
          this.onPreviousStep.emit(change);
          break;

        default:
          this.onStepChange.emit(change);
          break;
      }
    });
  }

  private setCurrentStep( step: WizardStepDirective ) {
    step.current = true;
    this.currentStep = step;
    this.currentStepIndex = step.index;
  }
}
