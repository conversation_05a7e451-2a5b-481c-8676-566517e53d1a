import { AfterViewInit, Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';

@Directive({
    selector: '[ifAllowed]',
    standalone: false
})
export class BaIfAllowedDirective implements AfterViewInit {

  @Input('ifAllowed')
  private permissions: string[];

  constructor( private view: ViewContainerRef,
               private template: TemplateRef<any>,
               private authService: SwHubAuthService,
  ) {
  }

  ngAfterViewInit(): void {
    const allowed = this.authService.allowedTo(this.permissions);
    if (allowed)
      setTimeout(() => {
        this.view.createEmbeddedView(this.template);
      });
  }
}
