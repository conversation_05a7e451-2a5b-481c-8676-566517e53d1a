import { DeploymentGroup } from '../typings/deployment-group';
import { Entity as EntityInterface } from '../typings/entity';
import { Jurisdiction } from '../typings/jurisdiction';
import { EntitySettingsModel } from './entity-settings.model';

export type EntityType = 'entity' | 'brand' | 'merchant' | 'liveStudio';
export type EntityStatus = 'normal' | 'suspended' | 'maintenance' | 'blocked_by_admin' | 'test';

/**
 * Extra area for Entity which contains specific merchant settings
 */
export class Merchant {
  type: string;
  code: string;
  lastTestsPassing?: string;
  params: { [field: string]: any };
  isTest: boolean = false;
}

export class CreateMerchantEntityData extends Merchant {
  name: string = '';
  description?: string = '';
  title?: string = '';
  defaultCurrency: string = 'USD';
  defaultCountry: string = 'US';
  defaultLanguage: string = 'en';
  languages?: string[] = [this.defaultLanguage];
  jurisdictionCode?: string = '';
  webSiteUrl?: string = '';
}

export class UpdateMerchantEntityData extends CreateMerchantEntityData {
  status: EntityStatus;
  countries?: string[] = [this.defaultCountry];
  currencies?: string[] = [this.defaultCurrency];
}

export class Entity implements EntityInterface {
  static TYPE_ENTITY: EntityType = 'entity';
  static TYPE_BRAND: EntityType = 'brand';
  static TYPE_MERCHANT: EntityType = 'merchant';

  static MASTER_NAME: string = 'MASTER';
  static ROOT_PATH: string = ':';

  id?: string;
  decryptedBrand?: string;
  name: string = '';
  type: EntityType = Entity.TYPE_ENTITY;
  title: string = '';
  description: string = '';
  path: string;
  status: EntityStatus = 'normal';
  key?: string;
  webSiteUrl?: string;
  isTest: boolean;
  isMerchant: boolean;
  settings: EntitySettingsModel;

  defaultCurrency: string = 'USD';
  defaultCountry: string = 'US';
  defaultLanguage: string = 'en';
  defaultBalance: {
    balance: number;
    currency: string;
  } = { balance: 0, currency: this.defaultCurrency };

  countries: string[] = [this.defaultCountry];
  currencies: string[] = [this.defaultCurrency];
  languages: string[] = [this.defaultLanguage];
  jurisdictions?: any[] = [];
  jurisdictionCode?: Jurisdiction;
  jurisdiction?: Jurisdiction[];

  balances?: Object = {};

  merchant?: Merchant;
  domains?: Array<string>;
  parentDomains?: Array<string>;
  merchantTypes?: string[];
  params?: { [key: string]: any };
  getEntityParent: () => Entity;
  deploymentGroup?: DeploymentGroup;

  parentId?: string;
  changeStatusState?: 'normal' | 'blocked' | 'blocked_by_parent' = 'normal';
  children?: string[];
  level?: number;

  dynamicDomainId?: string;
  staticDomainId?: string;
  lobbyDomainId?: string;
  liveStreamingDomainId?: string;
  ehubDomainId?: string;

  staticDomainPoolId?: string;
  dynamicDomainPoolId?: string;

  static getParentPath(path: string = ''): string {
    return path.split(':').slice(0, -2).join(':').concat(':');
  }

  constructor(entityData = {}) {
    Object.assign(this, entityData);
  }

  get entityParent(): Entity {
    return this.getEntityParent();
  }

  update(entityData) {
    Object.assign(this, entityData);
  }

  toJSON(): string {
    let data: Entity = Object.assign({}, this);
    let allowedProperties = [
      'type',
      'name',
      'description',
      'title',
      'status',
      'key',
      'defaultCurrency',
      'defaultCountry',
      'defaultLanguage',
      'countries',
      'currencies',
      // 'child',
      'languages',
      'path',
      'isMerchant',
      // 'balances',
      'domains',
      'merchantTypes',
      'jurisdictionCode',
      'webSiteUrl'
    ];

    Object.keys(data).forEach(propName => {
      if (allowedProperties.indexOf(propName) === -1) {
        delete data[propName];
      }
    });

    return JSON.stringify(data);
  }

  hasDefinedBalances(): boolean {
    return Object.keys(this.balances).length > 0;
  }

  isMaster(): boolean {
    return this.name === Entity.MASTER_NAME && this.isRoot();
  }

  isRoot(): boolean {
    return this.path === Entity.ROOT_PATH;
  }

  isOperator(): boolean {
    return this.type === Entity.TYPE_BRAND;
  }

  isReseller(): boolean {
    return this.type === Entity.TYPE_ENTITY;
  }

  getBalance(currencyCode: string): number {
    let balance = 0;

    if (this.isMaster()) {
      balance = +Infinity;
    } else if (this.balances && this.balances.hasOwnProperty(currencyCode)) {
      balance = this.balances[currencyCode].main;
    }

    return balance;
  }

  asCreateMerchantData(): CreateMerchantEntityData {
    let createMerchant = new CreateMerchantEntityData();

    Object.keys(createMerchant)
      .filter(field => this.hasOwnProperty(field))
      .forEach(field => createMerchant[field] = this[field]);

    createMerchant.type = this.merchant.type;
    createMerchant.code = this.merchant.code;
    createMerchant.params = this.merchant.params;

    return createMerchant;
  }

  asUpdateMerchantData(): UpdateMerchantEntityData {
    const updateMerchant = new UpdateMerchantEntityData();

    Object.keys(updateMerchant)
      .filter(field => this.hasOwnProperty(field))
      .forEach(field => updateMerchant[field] = this[field]);

    updateMerchant.params = this.merchant.params;

    return updateMerchant;
  }
}
