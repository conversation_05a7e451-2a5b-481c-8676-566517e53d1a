import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

// @see: https://github.com/tvicpe/nl2br-pipe

@Pipe({
    name: 'nl2br',
    standalone: false
})
export class Nl2BrPipe implements PipeTransform {

  constructor( private sanitizer: DomSanitizer ) {

  }

  transform( value: string ): SafeHtml {
    let replacedValue = value.replace(/(?:\r\n|\r|\n)/g, '<br />');
    return this.sanitizer.bypassSecurityTrustHtml( replacedValue );
  }
}
