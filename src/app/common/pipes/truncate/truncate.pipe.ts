import { Pipe, PipeTransform } from '@angular/core';
import { Truncate } from './truncate.interface';

@Pipe({
    name: 'truncate',
    standalone: false
})

export class TruncatePipe implements PipeTransform {

  transform(value: string, config: Truncate): string {
    const limit = config && config.maxLength ? config.maxLength : 255;
    const trail = config && config.isEllipsis ? '...' : '';
    return value.length > limit ? value.substring(0, limit) + trail : value;
  }
}
