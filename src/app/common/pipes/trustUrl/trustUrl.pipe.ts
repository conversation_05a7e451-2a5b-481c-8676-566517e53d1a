import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { SafeResourceUrl } from '@angular/platform-browser';

@Pipe({
    name: 'trustUrl', pure: false,
    standalone: false
})
export class TrustUrlPipe implements PipeTransform {
  constructor( private domSanitizer: DomSanitizer ) {
  }

  transform( value: string ): SafeResourceUrl {
    return this.domSanitizer.bypassSecurityTrustResourceUrl(value);
  }
}
