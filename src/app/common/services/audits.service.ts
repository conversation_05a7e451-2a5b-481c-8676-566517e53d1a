import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject, Observable } from 'rxjs';

import { BaseService } from '../base';
import { Audit as AuditTyping, BaseApiObject } from '../typings';
import moment from 'moment';
import { HttpClient } from '@angular/common/http';
import { map, share } from 'rxjs/operators';


const parser = require('ua-parser-js');
const AUDIT_SETTINGS_STORAGE_NAME = 'auditSettings';


@Injectable()
export class AuditService<T extends AuditTyping & BaseApiObject> extends BaseService<T> {
  public urlList: string = '/audits/';
  public urlGet: string = '/audits/';
  public urlSave: string = '/audits/';
  public urlCreate: string = '/audits/';
  public urlRemove: string = '/audits/';

  public _item: Subject<T>;
  public _items: Subject<T[]>;

  public auditSettings = Object.assign({},
    JSON.parse(localStorage.getItem(AUDIT_SETTINGS_STORAGE_NAME))
  );

  private baseApiEndpoint: string = BaseService.apiEndpoint;

  constructor( public notifications: SwuiNotificationsService,
               public http: HttpClient
  ) {
    super(notifications, http);
  }


  public setStatus( audit, status: string ) {
    let fn = this.processRecord;

    const method = status === 'normal' ? 'DELETE' : 'PUT';
    const statusObservable = this.http
      .request<T>(method, `${this.baseApiEndpoint}/audits/${audit.auditname}/suspended`).pipe(
        map(response => fn(response))
      );

    statusObservable
      .subscribe(
        data => console.log('Ok.', data),
        err => this.handleErrors.call(this, err)
      );

    return statusObservable;
  }

  public processRecord( record: any ): T {
    record._meta = {};
    const ua = parser(record.userAgent);
    const { os: { name, version } } = ua;

    record.system = `${name || ''} ${version || ''}`;
    record.history = !!record.history ? record.history : {};
    record.historyMessage = this.getAuditHistoryMessage(record);

    if (record.auditsSummary) {
      record.eventName = record.auditsSummary.eventName;
      record.summary = record.auditsSummary.summary;
      record.path = record.auditsSummary.path;
      record.method = record.auditsSummary.method;
    }

    record._meta.ts = moment(record.ts);

    return record as T;
  }

  public getList( filter?): Observable<T[]> {

    let fn = this.processRecord.bind(this);

    this.requestParams = BaseService.getRequestParams(filter);

    let { values: { path } } = filter;
    const url: string = this.getUrl(path);

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          const data = response.body.map(fn) as T[];
          this.responseHeaders = this.getResponseParams(response, filter, data);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  private getAuditHistoryMessage( record: any ): string {
    if (record.history && typeof record.history === 'string') {
      record.history = this.stringToObject(record.history);
    }
    let message = '-';
    // Do not trust the backend!!
    try {
      let { auditType } = record;
      switch (auditType) {
        case 'entity_create':
          const { history: { result: { name: entityCreateName } } } = record;
          message = `Entity ${entityCreateName} created`;
          break;
        case 'record':
          const { history: { result: { name: recordName } } } = record;
          message = `Brand ${recordName} created`;
          break;
        case 'entity_merchant_create':
          const { history: { result: { name: merchantCreateName } } } = record;
          message = `Merchant ${merchantCreateName} created`;
          break;
        case 'entity_language_add':
          const { history: { parameters: { code: languageAddCode } } } = record;
          message = `Language ${languageAddCode} added`;
          break;
        case 'entity_language_remove':
          const { history: { parameters: { code: languageRemoveCode } } } = record;
          message = `Language ${languageRemoveCode} removed`;
          break;
        case 'entity_currency_add':
          const { history: { parameters: { code: currencyAddCode } } } = record;
          if (Array.isArray(currencyAddCode)) {
            message = `Currencies ${currencyAddCode.join(', ')} added`;
          } else {
            message = `Currency ${currencyAddCode} added`;
          }
          break;
        case 'entity_currency_remove':
          const { history: { parameters: { code: currencyRemoveCode } } } = record;
          if (Array.isArray(currencyRemoveCode)) {
            message = `Currencies ${currencyRemoveCode.join(', ')} removed`;
          } else {
            message = `Currency ${currencyRemoveCode} removed`;
          }
          break;

        case 'entity_country_add':
          const { history: { parameters: { code: countryAddCode } } } = record;
          if (Array.isArray(countryAddCode)) {
            message = `Countries ${countryAddCode.join(', ')} added`;
          } else {
            message = `Country ${countryAddCode} added`;
          }
          break;
        case 'entity_country_remove':
          const { history: { parameters: { code: countryRemoveCode } } } = record;
          if (Array.isArray(countryRemoveCode)) {
            message = `Countries ${countryRemoveCode.join(', ')} removed`;
          } else {
            message = `Country ${countryRemoveCode} removed`;
          }
          break;
        case 'entity_credit':
          const {
            history: {
              parameters: {
                currency: entityCreditCurrency,
                amount: entityCreditAmount
              }
            }
          } = record;
          message = `Credit: ${entityCreditCurrency} ${entityCreditAmount}`;
          break;
        case 'entity_debit':
          const {
            history: {
              parameters: {
                currency: entityDebitCurrency,
                amount: entityDebitAmount
              }
            }
          } = record;
          message = `Debit: ${entityDebitCurrency} ${entityDebitAmount}`;
          break;
        case 'player_change_status':
          let playerChangeStatusStatus, playerChangeStatusCode;
          if ('changes' in record.history) {
            playerChangeStatusStatus = record.history.changes[0]['value'];
            playerChangeStatusCode = record.history.playerId;
          } else if ('result' in record.history) {
            playerChangeStatusStatus = record.history.result.status;
            playerChangeStatusCode = record.history.parameters.code;
          }
          message = `${playerChangeStatusCode}: ${playerChangeStatusStatus}`;
          break;

        case 'entity_settings_change':
          message = ``;
          break;

        case 'entity_settings_reset':
          message = ``;
          break;

        case 'user_create':
          const { history: { result: { username } } } = record;
          message = `${username} created`;
          break;


        default:
          message = '';
      }
    } catch (error) {
      console.error(error);
    }
    return message;
  }
}
