import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, share } from 'rxjs/operators';
import { Observable } from 'rxjs/Observable';
import moment from 'moment';

import { BaseApiObject } from '../typings';
import { BaseService } from '../base';
import { Grc, GrcDefinition, GrcEmailStart } from '../typings/grc-config';
import { GridDataService, SwuiNotificationsService } from '@skywind-group/lib-swui';

const CHALLENGES_URL = '/api/v1/srt/challenges';
const EMAIL_SEND_URL = '/emails/send';

@Injectable()
export class GrcService<T extends Grc & BaseApiObject> extends BaseService<T> implements GridDataService<Grc> {
  public challengesUrl: string = CHALLENGES_URL;

  private baseApiEndpoint: string = BaseService.apiEndpoint;

  constructor( public notifications: SwuiNotificationsService, public http: HttpClient ) {
    super(notifications, http);
  }

  public getGridData( params: HttpParams ): Observable<HttpResponse<Grc[]>> {
    const url = `${this.challengesUrl}`;

    return this.http.get<Grc[]>(`${url}`, { params, observe: 'response' });
  }

  public getList( filter?, _?: any ): Observable<T[]> {

    let fn = this.processRecord.bind(this);

    this.requestParams = BaseService.getRequestParams(filter);

    const url = `${this.challengesUrl}`;

    const source = this.http
      .get<T[]>(url, {
        params: this.requestParams,
        observe: 'response'
      }).pipe(
        map(response => {
          let data = response.body;
          this.responseHeaders = this.getResponseParams(response, { pages: {} }, data);
          data = data.map(fn);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );
    source.subscribe(
      data => this._items.next(data),
      err => this.handleErrors.call(this, err)
    );
    return source;
  }

  public setNewGrc( body?: any ) {
    let url = `${this.challengesUrl}`;

    delete body._meta;

    Object.assign(body, {
      startAt: BaseService.convertDateToBackendValue(body.startAt),
      finishAt: BaseService.convertDateToBackendValue(body.finishAt),
    });

    const status$ = this.http.post(url, body).pipe(
      share()
    );
    status$.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );

    return status$;
  }

  public deleteItem( challengeId ): Observable<any> {
    let url = `${this.challengesUrl}/${challengeId}`;
    const source = this.http.delete(url).pipe(
      share()
    );

    source.subscribe(
      () => {
      },
      this.handleErrors.bind(this)
    );

    return source;
  }

  public getChallengeById( id: string ): Observable<T> {
    const fn = this.processRecord.bind(this);

    const source = this.http
      .get<T>(`${this.challengesUrl}/${id}`, {
        observe: 'response'
      }).pipe(
        map(response => {
          this.responseHeaders = this.getResponseParams(response);
          const data = fn(response.body);
          this._httpResponse.next(this.responseHeaders);
          return data;
        }),
        share()
      );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public processRecord( record: any ): T {
    record.startAt = record.startAt ? moment(record.startAt) : '';
    record.finishAt = record.finishAt ? moment(record.finishAt) : '';
    record.logoUrl = (record.definition && record.definition.logoUrl) ?
      record.definition.logoUrl : '';
    record._meta = {
      startAt: record.startAt,
      finishAt: record.finishAt,
      logoUrl: record.logoUrl
    };
    return record as T;
  }

  public getEmailTemplates( path, emailType: string, grcEmailConfig?: GrcEmailStart ): Observable<T> {
    console.log('GRC service: get email templates ', path);

    let params = new HttpParams();
    if (grcEmailConfig) {
      Object.keys(grcEmailConfig).forEach(key => {
        params = params.set(key, grcEmailConfig[key]);
      });
    }

    const url = `${this.challengesUrl}/${path}/templates/${emailType}`;

    const source = this.http
      .get<T>(url, { params }).pipe(
        share()
      );

    source.subscribe(
      data => this._item.next(data),
      err => this.handleErrors.call(this, err)
    );

    return source;
  }

  public sendEmails( email ) {
    let url = `${this.baseApiEndpoint}${EMAIL_SEND_URL}`;

    const status$ = this.http.post(url, email).pipe(
      share()
    );
    status$.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );

    return status$;
  }

  public updateDefinition( path: string, definition: GrcDefinition ) {
    let url = `${this.challengesUrl}/${path}/definition`;

    const source = this.http.put(url, definition).pipe(
      share()
    );

    source.subscribe(
      () => {
      },
      err => this.handleErrors.call(this, err)
    );

    return source;
  }
}
