import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GridRequestData, SettingsService, SwuiGridDataService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { Observable } from 'rxjs';
import { API_ENDPOINT, FORMAT_DATETIME } from '../../../app.constants';
import { FinancialReportItem } from '../../typings';
import { CsvSchema, CsvService, transformDate } from '../csv.service';
import { getCurrencyLabel, transformFormatCurrencyValue } from '../../core/currecy-transform';

const csvSchema = ( timezoneName: string, format: string ): CsvSchema[] => [
  {
    title: 'REPORT_FINANCIAL.FILTER.path',
    name: 'path',
  },
  {
    title: 'REPORT_FINANCIAL.FILTER.type',
    name: 'type',
  },
  {
    title: 'REPORT_FINANCIAL.GRID.date',
    name: 'ts',
    transform: transformDate(timezoneName, format),
  },
  {
    title: 'REPORT_FINANCIAL.GRID.from',
    name: 'fromEntityInfo',
    transform( data: { name: string } ) {
      return data?.name || '';
    }
  },
  {
    title: 'REPORT_FINANCIAL.GRID.to',
    name: 'toEntityInfo',
    transform( data: { name: string } ) {
      return data?.name || '';
    }
  },
  {
    title: 'REPORT_FINANCIAL.GRID.initiatorName',
    name: 'initiatorName',
  },
  {
    title: 'REPORT_FINANCIAL.GRID.currency',
    name: 'currency',
    transform(data: any): string {
      return getCurrencyLabel(data);
    }
  },
  {
    title: 'PAYMENTS_TRANSFERS.GRID.amount',
    name: 'amount',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
];

function getUrl( path?: string ): string {
  return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/finance`;
}

@Injectable()
export class ReportFinancialService implements SwuiGridDataService<FinancialReportItem> {
  constructor( private http: HttpClient,
               private readonly csvService: CsvService,
               private setting: SettingsService
  ) {
  }

  public getGridData( params: HttpParams, data?: GridRequestData ): Observable<HttpResponse<FinancialReportItem[]>> {
    let path;

    if (typeof data !== 'undefined' && data.hasOwnProperty('path')) {
      path = data['path'];
    } else if (params.has('path')) {
      path = params.get('path');
    }

    return this.http.get<FinancialReportItem[]>(getUrl(path), {
      params,
      observe: 'response'
    });
  }

  public exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const fileName = `Export Financial report ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    const { appSettings: { dateFormat, timeFormat, timezoneName } } = this.setting;
    const datetimeFormat = dateFormat && timeFormat ? `${dateFormat} ${timeFormat}` : FORMAT_DATETIME;
    this.csvService.exportToCsv(csvSchema(timezoneName, datetimeFormat), data, fileName, columns);
  }
}
