import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { GridRequestData, SwuiGridDataService } from '@skywind-group/lib-swui';
import moment from 'moment';
import { of } from 'rxjs';
import { tap } from 'rxjs/operators';

import { API_ENDPOINT } from '../../../app.constants';
import { Entity } from '../../models/entity.model';
import { PlayerReportItem } from '../../typings';
import { CsvSchema, CsvService } from '../csv.service';
import { Observable } from 'rxjs/Observable';
import { getCurrencyLabel, transformFormatCurrencyValue } from '../../core/currecy-transform';

const csvSchema = ( rtp: CsvSchema ): CsvSchema[] => [
  {
    name: 'playerCode',
    title: 'REPORT_PLAYERS.CSV.playerCode',
  },
  {
    name: 'debits',
    title: 'REPORT_PLAYERS.CSV.debits',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    name: 'credits',
    title: 'REPORT_PLAYERS.CSV.credits',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    name: 'currency',
    title: 'REPORT_PLAYERS.CSV.currency',
    transform(data: any): string {
      return getCurrencyLabel(data);
    }
  },
  {
    name: 'playedGames',
    title: 'REPORT_PLAYERS.CSV.playedGames',
  },
  {
    name: 'totalBets',
    title: 'REPORT_PLAYERS.CSV.totalBets',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    name: 'totalWins',
    title: 'REPORT_PLAYERS.CSV.totalWins',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    name: 'totalJpWins',
    title: 'REPORT_PLAYERS.CSV.totalJpWins',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  {
    name: 'GGR',
    title: 'REPORT_PLAYERS.CSV.GGR',
    transform(data: any, rows: Record<string, any>): string {
      return transformFormatCurrencyValue(data, rows.currency);
    }
  },
  rtp,
];

function getUrl( path?: string ): string {
  return `${API_ENDPOINT}${path && path !== ':' ? '/entities/' + path : ''}/report/players`;
}

@Injectable()
export class ReportPlayerService implements SwuiGridDataService<PlayerReportItem> {
  private readonly entityType: 'entity' | 'brand' | 'merchant';
  private _blocked = true;

  constructor( { snapshot: { data: { brief } } }: ActivatedRoute,
               private readonly http: HttpClient,
               private csvService: CsvService,
  ) {
    this.entityType = brief.type;
  }

  blockData() {
    this._blocked = true;
  }

  unblockData() {
    this._blocked = false;
  }

  getGridData( params: HttpParams, requestData?: GridRequestData ) {
    const isAllowed = (Object.keys(requestData).length !== 0 && requestData.type !== Entity.TYPE_ENTITY) ||
      this.entityType !== Entity.TYPE_ENTITY;

    if (requestData && isAllowed && !this._blocked) {
      return this.http.get<PlayerReportItem[]>(`${getUrl(requestData.path)}`, {
        params,
        observe: 'response'
      }).pipe(
        tap(resp => {
          resp.body.forEach(( player: PlayerReportItem ) => {
            player._meta = { fullPath: requestData?.path || '', type: requestData.type };
            this.processRecord(player);
          });
        }),
      );
    }
    return of(new HttpResponse<PlayerReportItem[]>({
      body: []
    }));
  }

  public downloadCsv(): Observable<any> {
    const schema = csvSchema({
      name: 'RTP',
      title: 'REPORT_PLAYERS.CSV.RTP',
      transform(data: any): string {
        return transformFormatCurrencyValue(data * 100, 'USD');
      }
    });
    const fileName = `Export Players report ${moment().format('YYYY-MM-DD HH:MM')}`;
    return this.csvService.download(getUrl, schema, fileName);
  }

  public exportPage( data: Record<string, any>[], columns: string[], page: number ) {
    const fileName = `Export Players report ${moment().format('YYYY-MM-DD HH:MM')} (page ${page})`;
    const schema = csvSchema({
      name: 'rtpPerc',
      title: 'REPORT_PLAYERS.CSV.RTP',
      transform(val: any): string {
        return transformFormatCurrencyValue(val, 'USD');
      }
    });
    this.csvService.exportToCsv(schema, data, fileName, columns);
  }

  private processRecord( record ): PlayerReportItem {
    record.playedGames = parseInt(record.playedGames || 0, 10).toFixed(0);
    record.totalBets = parseFloat(record.totalBets || 0).toFixed(2);
    record.totalWins = parseFloat(record.totalWins || 0).toFixed(2);
    record.credits = parseFloat(record.credits || 0).toFixed(2);
    record.debits = parseFloat(record.debits || 0).toFixed(2);
    record.GGR = parseFloat(record.GGR || 0).toFixed(2);
    record.rtpPerc = Math.round(parseFloat(record.RTP || 0) * 100).toFixed(2);
    return record;
  }
}
