import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';

import moment from 'moment';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { PERMISSIONS_NAMES } from '../../app.constants';
import { NoticeSettings } from '../../pages/player/components/customer-page/customer-resp-gaming/notice/notice-settings';
import { BaseService } from '../base';
import { ResponsibleGamingModel } from '../models/responsible-gaming.model';
import { ResponsibleGaming } from '../typings/responsible-gaming';
import { ValidationService } from './validation.service';

@Injectable()
export class ResponsibleGamingService<T extends ResponsibleGaming> extends BaseService<T> {

  public urlGet: string = '/responsiblegaming/';
  public urlSave: string = '/responsiblegaming';
  public urlRemove: string = '/responsiblegaming/';
  public urlCreate: string = '/responsiblegaming/';
  public serviceName: string = 'ResponsibleGaming';
  public allowedEditPermissions: string[] = [PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT];

  private timeframeStrictnessWeights = new Map([
    ['daily', 5],
    ['weekly', 4],
    ['monthly', 3]
  ]);

  constructor( public notifications: SwuiNotificationsService,
               public authService: SwHubAuthService,
               public http: HttpClient,
  ) {
    super(notifications, http);
  }

  public processRecord( record ): T {
    record._meta = {};
    return new ResponsibleGamingModel(record) as T;
  }

  public deletePending( id: string, body: Object ): Observable<T> {
    const url = this.getItemURL(id, {}, this.urlCreate);
    return this.http.request<T>('DELETE', url, { body }).pipe(
        catchError(this.handleErrors.bind(this)),
      );
  }

  public newSettingRequiresCoolingOff( currentValue: number,
                                       currentTimeframe: string,
                                       newSettingValue: number,
                                       newSettingTimeframe: string
  ): boolean {
    if (currentValue === null) {
      return false;
    }
    if (newSettingValue === null) {
      return true;
    }
    if (currentTimeframe === newSettingTimeframe) {
      return newSettingValue > currentValue;
    }

    function getValuePerDay( value: number, timeframe: string ): number {
      let valuePerDay = value;

      if (timeframe === 'weekly') {
        valuePerDay = value / 7;
      } else if (timeframe === 'monthly') {
        valuePerDay = value / 30;
      }
      return valuePerDay;
    }

    let currentValuePerDay = getValuePerDay(currentValue, currentTimeframe);
    let newValuePerDay = getValuePerDay(newSettingValue, newSettingTimeframe);

    if (currentValuePerDay === newValuePerDay) {
      return this.timeframeStrictnessWeights.get(currentTimeframe) >
        this.timeframeStrictnessWeights.get(newSettingTimeframe);
    }
    return newValuePerDay > currentValuePerDay;
  }

  public dateFromNowByDays( days: number ): string {
    const dateAdded = moment(Date.now()).add(days, 'days');
    return moment.utc(dateAdded).format();
  }

  public dateFromNowByMonths( months: number ): string {
    const dateAdded = moment(Date.now()).add(months, 'months');
    return moment.utc(dateAdded).format();
  }

  public dateFromNowByYears( years: number ): string {
    const dateAdded = moment(Date.now()).add(years, 'years');
    return moment.utc(dateAdded).format();
  }

  public daysFromDateTillNow( date: string ): number {
    if (date) {
      const a = moment(date);
      const b = moment(Date.now());
      return Math.abs(a.diff(b, 'days')) + 1;
    } else {
      return null;
    }
  }

  public formatDateDMY( date: string ): string {
    return moment(date).format('DD/MM/YYYY');
  }

  public formatDateDMYHM( date: string ): string {
    return moment(date).format('DD/MM/YYYY HH:mm');
  }

  public getTitleByValue( arr: Array<Object>, value: any ): string {
    if (value !== null) {
      return arr.find(x => x['value'] === value)['title'];
    }
  }

  public checkNewTimeframe( control: FormControl, timeFrame: string ): void {
    if (timeFrame === '') {
      control.clearValidators();
      control.updateValueAndValidity();
    } else {
      control.setValidators([Validators.required, ValidationService.numbersOnlyValidator]);
    }
  }

  public isPending( pendingDate: Date ): boolean {
    return !!pendingDate;
  }

  public isLimitSet( limit: number ): boolean {
    return limit !== null;
  }

  public setNotice( isButtonEnable ): NoticeSettings {
    return { isButtonEnable: isButtonEnable };
  }

  public setLimitNotice( limit: number, pendingDate: Date, isButtonEnable: boolean ): NoticeSettings {

    if (this.isLimitSet(limit)) {
      if (this.isPending(pendingDate)) {
        return {
          isButtonEnable: isButtonEnable,
          buttonTitle: 'Cancel pending change',
          class: 'pending',
        };
      }
      return { isButtonEnable: isButtonEnable, buttonTitle: 'Change limit' };
    }
  }

  public isEditEnabled( selfExclusion: string ): boolean {
    if (!this.authService.allowedTo(this.allowedEditPermissions) || selfExclusion !== '') {
      return false;
    } else {
      return true;
    }
  }

}


