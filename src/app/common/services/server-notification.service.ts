import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { TOKEN_REFRESH_TIMEOUT } from '../../app.constants';

import { BaseService } from '../base';
import moment from 'moment';
import { HttpClient } from '@angular/common/http';

const COMMON_MESSAGE_URL = '/json/messages';

@Injectable()
export class ServerNotificationService<ServerNotification> extends BaseService<ServerNotification> {
  public urlList = COMMON_MESSAGE_URL;
  public urlGet = COMMON_MESSAGE_URL;
  public urlSave = COMMON_MESSAGE_URL;
  public urlCreate = COMMON_MESSAGE_URL;
  public urlRemove = COMMON_MESSAGE_URL;

  public _notDisplayedItems: Subject<ServerNotification[]>;

  constructor( public notifications: SwuiNotificationsService, public http: HttpClient ) {
    super(notifications, http);

    this._notDisplayedItems = <Subject<ServerNotification[]>>new Subject();
    this.items.subscribe(this.setNotDisplayedItems.bind(this));
  }

  public processRecord( record ): ServerNotification {
    let dateKeys = ['displayDate', 'creationDate', 'publicationDate'];
    dateKeys.forEach(key => record[key] = moment(record[key]));
    return record as ServerNotification;
  }

  get notDisplayedItems() {
    return this._notDisplayedItems.asObservable();
  }

  private setNotDisplayedItems( data ) {
    const TIME_DIFF = Date.now() + TOKEN_REFRESH_TIMEOUT / 2;
    let notDisplayedData = data
      .filter(d => !d.displayDate || d.displayDate > TIME_DIFF)
      .sort(( a, b ) => {
        return a.creationDate > b.creationDate ? -1 : 1;
      });

    this._notDisplayedItems.next(notDisplayedData);
  }
}
