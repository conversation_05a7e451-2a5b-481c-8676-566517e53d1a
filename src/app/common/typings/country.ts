import { BaseApiObject } from './base';

export interface Country extends BaseApiObject {
  code: string;
  displayName: string;
}

export interface InheritedCountryCode {
  code: string;
  inherited: boolean;
}

export function getInheritedCountryCode(ownCountries: string[], inheritedCountries: string[]): InheritedCountryCode[] {
  const data = inheritedCountries?.reduce<Record<string, boolean>>((result, code) => ({
    ...result,
    [code]: true
  }), {}) || {};
  for (const code of ownCountries ?? []) {
    data[code] = false;
  }
  return Object.entries(data).map<InheritedCountryCode>(([code, inherited]) => ({ code, inherited }));
}
