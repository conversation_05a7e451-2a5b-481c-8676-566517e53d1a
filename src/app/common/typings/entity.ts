import { BaseApiObject } from './base';
import { Jurisdiction } from './jurisdiction';


export interface Entity extends BaseApiObject {
  id?: string;
  name: string;
  type?: string;
  title?: string;
  description?: string;
  path?: string;
  status: string;
  key?: string;
  parent?: string;
  webSiteUrl?: string;
  isMerchant: boolean;

  defaultCurrency: string;
  defaultCountry: string;
  defaultLanguage: string;
  defaultBalance: {
    balance: number;
    currency: string;
  };

  countries?: string[];
  currencies?: string[];
  languages?: string[];
  child?: Entity[];
  domains?: Array<string>;
  jurisdictions?: Jurisdiction[];
  jurisdictionCode?: Jurisdiction;
  merchantTypes?: string[];
  params?: { [key: string]: any };

  dynamicDomainId?: string;
  staticDomainId?: string;
  lobbyDomainId?: string;
  liveStreamingDomainId?: string;
  ehubDomainId?: string;

  staticDomainPoolId?: string;
  dynamicDomainPoolId?: string;
}

export interface GameLogoutOptions {
  type: 'all' | 'unfinished';
  maxRetryAttempts: number;
  maxSessionTimeout: number;
}


export interface EntityShortInterface {
  id: string;
  name: string;
  path: string;
  title: string;
  type: string;
  status: string;
  key?: string;
  dynamicDomainId?: string;
  staticDomainId?: string;
  child: EntityShortInterface[];
}
