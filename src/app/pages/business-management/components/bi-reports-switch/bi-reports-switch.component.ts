import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { PERMISSIONS_NAMES, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { take } from 'rxjs/operators';
import { BiReportDomain } from '../../../../common/typings/bi-report-domain';
import { BiReportsSwitchService } from './bi-reports-switch.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'bi-reports-switch',
    templateUrl: './bi-reports-switch.component.html',
    styleUrls: ['./bi-reports-switch.component.scss'],
    standalone: false
})
export class BIReportsSwitchComponent implements OnInit {
  reports: BiReportDomain[];

  loaded = false;
  selectedControl: FormControl;
  readonly canSelect: boolean;

  constructor( private biReportsSwitchService: BiReportsSwitchService,
               private notificationsService: SwuiNotificationsService,
               private translate: TranslateService,
               auth: SwHubAuthService ) {
    this.canSelect = auth.allowedTo([PERMISSIONS_NAMES.KEYENTITY_BI_REPORT_DOMAINS, PERMISSIONS_NAMES.KEYENTITY_BI_REPORT_DOMAINS_SELECT]);
    if (!this.canSelect) {
      this.selectedControl.disable();
    }
  }

  ngOnInit(): void {
    this.loaded = false;
    this.biReportsSwitchService.getReports()
      .pipe(take(1))
      .subscribe(data => {
        this.reports = data;
        if (this.canSelect) {
          const selectedPid = data.find(( { isSelected } ) => isSelected);
          this.selectedControl = new FormControl(selectedPid?.pid || '');
        }
        this.loaded = true;
      });
  }

  onSave() {
    this.biReportsSwitchService.selectReports(this.selectedControl.value)
      .pipe(take(1))
      .subscribe(() => {
        this.notificationsService.success(
          this.translate.instant('BI-REPORTS-SWITCH.successSaveMessage', {pair: this.selectedControl.value})
        );
        this.selectedControl.markAsPristine();
      });
  }
}
