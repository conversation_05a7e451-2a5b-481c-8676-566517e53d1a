import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiGamesSelectManagerModule, SwuiPagePanelModule } from '@skywind-group/lib-swui';
import { HintsModule } from '../../../../common/components/hints/hints.module';

import { ShortStructureResolver } from '../../../../common/services/resolvers/structure.resolver';
import { EntityPagePanelModule } from '../entity-page-panel/entity-page-panel.module';
import { CascadeGamesComponent } from './cascade-games.component';
import { CascadeGamesRouting } from './cascade-games.routing';

@NgModule({
  imports: [
    CommonModule,
    CascadeGamesRouting,
    HintsModule,
    TranslateModule,
    SwuiGamesSelectManagerModule,
    SwuiControlMessagesModule,
    MatButtonModule,
    LayoutModule,
    SwuiPagePanelModule,
    EntityPagePanelModule
  ],
  exports: [],
  declarations: [CascadeGamesComponent],
  providers: [
    ShortStructureResolver,
  ],
})
export class CascadeGamesModule {
}
