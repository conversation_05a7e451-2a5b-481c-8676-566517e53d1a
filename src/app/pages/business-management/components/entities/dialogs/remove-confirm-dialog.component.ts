import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface RemoveConfirmDialogData {
  removeCode: string;
}

@Component({
    selector: 'remove-confirm-dialog',
    templateUrl: 'remove-confirm-dialog.component.html',
    standalone: false
})
export class RemoveConfirmDialogComponent implements OnInit {
  removeCode: string;

  constructor(
    @Inject(MAT_DIALOG_DATA) data: RemoveConfirmDialogData,
    public dialogRef: MatDialogRef<RemoveConfirmDialogComponent>,
  ) {
    this.removeCode = data.removeCode;
  }

  ngOnInit() {
  }

  submitRemove() {
    this.dialogRef.close(this.removeCode);
  }
}
