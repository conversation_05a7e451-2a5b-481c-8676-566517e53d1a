import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Subject, timer } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { EntityBreadcrumb, LOCALSTORAGE_ENTITY_PATH } from '../entity-breadcrumbs.model';

@Component({
    selector: 'sw-entity-breadcrumbs-item',
    templateUrl: './entity-breadcrumbs-item.component.html',
    styleUrls: ['./entity-breadcrumbs-item.component.scss'],
    standalone: false
})
export class EntityBreadcrumbsItemComponent implements OnInit {
  @Input()
  set item( val: EntityBreadcrumb ) {
    this._item = val;
    this.filteredSingleLevelItems = val.singleLevelItems;
  }

  get item(): EntityBreadcrumb {
    return this._item;
  }

  @ViewChild('list', { read: ElementRef }) listElement: ElementRef<HTMLDivElement>;

  searchControl = new FormControl();
  filteredSingleLevelItems: EntityBreadcrumb[] = [];
  disabled = true;
  private _item: EntityBreadcrumb;
  private readonly _destroyed$ = new Subject<void>();

  constructor( private router: Router,
               private authService: SwHubAuthService
  ) {
  }

  ngOnInit(): void {
    this.disabled = this.item.path === ':' && this.authService.isSuperAdmin;
    this.searchControl.valueChanges
      .pipe(
        map(( val: string ) => val ? val.trim().toLowerCase() : ''),
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: string ) => {
        const sourceItems = this.item?.singleLevelItems || [];
        this.filteredSingleLevelItems = sourceItems.filter(( item: EntityBreadcrumb ) => {
          return val ? (item.name.toLowerCase().startsWith(val) || item.title.toLowerCase().startsWith(val)) : true;
        });
      });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  onLinkClick( event: MouseEvent, item: EntityBreadcrumb, isChild?: boolean ) {
    event.preventDefault();
    if (!!isChild) {
      localStorage.removeItem(LOCALSTORAGE_ENTITY_PATH);
    }

    this.router.navigate(['pages', 'business-management', 'entities', 'setup', item.path, 'p']);
  }

  onSelectButtonClick( event: MouseEvent ) {
    event.stopPropagation();
    const selectedIndex = this.filteredSingleLevelItems.findIndex(( { path } ) => path === this.item.path) - 1;
    if (selectedIndex > 0) {
      this.listElement?.nativeElement?.scrollTo(0, 36 * selectedIndex);
    }
  }

  prevent( event: MouseEvent ) {
    event.preventDefault();
    event.stopPropagation();
  }

  onMenuClosed() {
    timer(300).subscribe(() => this.searchControl.reset());
  }
}
