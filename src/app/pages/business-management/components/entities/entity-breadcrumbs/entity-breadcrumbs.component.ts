import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Entity } from '../../../../../common/models/entity.model';
import { ExtendedEntity } from '../../mat-business-structure/business-structure.service';
import { EntityBreadcrumb, FlatEntity, LOCALSTORAGE_ENTITY_PATH } from './entity-breadcrumbs.model';


@Component({
    selector: 'sw-entity-breadcrumbs',
    templateUrl: './entity-breadcrumbs.component.html',
    styleUrls: ['./entity-breadcrumbs.component.scss'],
    standalone: false
})
export class EntityBreadcrumbsComponent implements OnInit, OnDestroy {
  breadcrumbs: EntityBreadcrumb[] = [];
  isCollapsed = true;
  entitiesObject;
  activeBreadCrumbIndex$ = new BehaviorSubject<number>(0);
  emptyBreadcrumb: EntityBreadcrumb | null = null;

  private _flatStructure: FlatEntity[];
  private _activePath: string;
  private readonly _destroyed$ = new Subject<void>();

  constructor( private activatedRoute: ActivatedRoute,
               private notificationsService: SwuiNotificationsService
  ) {
    const { snapshot: { data: { activeShortStructure } } } = this.activatedRoute;
    this._flatStructure = this.convertToFlatStructure(activeShortStructure);
  }

  ngOnInit(): void {
    this.activatedRoute.params
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(val => {
        this._activePath = (!val?.path || val.path === ':') ? '' : val.path;
        const storagePath: string = localStorage.getItem(LOCALSTORAGE_ENTITY_PATH);
        if (!storagePath || !storagePath?.startsWith(this._activePath)) {
          localStorage.setItem(LOCALSTORAGE_ENTITY_PATH, this._activePath);
        }
        this.breadcrumbs = this.getBreadcrumbs(localStorage.getItem(LOCALSTORAGE_ENTITY_PATH), this._activePath);
        this.activeBreadCrumbIndex$.next(this.breadcrumbs.findIndex(( el: EntityBreadcrumb ) => el.isActive));

        const lastBreadcrumb = this.breadcrumbs.slice(-1)[0];

        if (lastBreadcrumb?.children?.length) {
          this.emptyBreadcrumb = this.getEmptyBreadCrumb(lastBreadcrumb);
        } else {
          this.emptyBreadcrumb = null;
        }
      });

    this.activeBreadCrumbIndex$
      .pipe(
        takeUntil(this._destroyed$)
      ).subscribe(val => {
      if (val < 4) {
        this.isCollapsed = false;
      }
    });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
    localStorage.removeItem(LOCALSTORAGE_ENTITY_PATH);
  }

  onEllipsisClick( event: MouseEvent ) {
    event.preventDefault();
    this.isCollapsed = false;
  }

  getEmptyBreadCrumb( parent: EntityBreadcrumb ): EntityBreadcrumb {
    return {
      singleLevelItems: parent?.children || []
    };
  }

  copyPath( event: MouseEvent ) {
    event.preventDefault();
    const el = document.createElement('textarea');
    el.value = this._activePath;
    el.setAttribute('readonly', '');
    el.style.position = 'absolute';
    el.style.left = '-9999px';
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);

    this.notificationsService.success('Entity path was copied to clipboard');
  }

  isBreadCrumbCollapsed( item: EntityBreadcrumb, index: number ) {
    if (this.isCollapsed && this.breadcrumbs.length >= 4) {
      return !item.isActive && index < (this.activeBreadCrumbIndex$.value - 2);
    }
    return false;
  }

  getShiftedBreadCrumbsArray(): EntityBreadcrumb[] {
    const array: EntityBreadcrumb[] = [...this.breadcrumbs];
    array.shift();
    return array;
  }

  onCollapseClick( event: MouseEvent ) {
    event.preventDefault();
    this.isCollapsed = true;
  }

  private getTextWithoutLastColon( path: string, name?: string ) {
    if (path) {
      if (path === ':' && name) {
        return name;
      }

      if (path.slice(-1) === ':') {
        return path.slice(0, -1);
      } else {
        return path;
      }
    }

    return '';
  }

  private getBreadcrumbs( initialSourcePath: string, activePath: string ): EntityBreadcrumb[] {
    const sourcePath = ':' + initialSourcePath;
    const source = sourcePath.split(':').slice(0, -1).map(el => el + ':');

    return source.map(( path: string, index ) => {
      const currentPath = source.slice(index ? 1 : 0, index + 1).join('');
      const parentPath = source.slice(index > 1 ? 1 : 0, index).join('');

      const parentEntity: FlatEntity = this._flatStructure.find(el => el.path === parentPath);
      const currentEntity: FlatEntity = this._flatStructure.find(el => el.path === currentPath);

      const singleLevelItems = this.getChildrenBreadcrumbs(parentEntity ? parentEntity.childrenPaths : [], activePath);
      const children = this.getChildrenBreadcrumbs(currentEntity ? currentEntity.childrenPaths : [], activePath);

      return {
        path: currentPath,
        name: this.getTextWithoutLastColon(path, currentEntity.name),
        title: currentEntity.title,
        isActive: this.getTextWithoutLastColon(currentPath) === this.getTextWithoutLastColon(activePath),
        children,
        singleLevelItems
      };
    });
  }

  private getChildrenBreadcrumbs( paths: string[], activePath: string ): EntityBreadcrumb[] {
    return paths.map(( itemPath: string ) => {
      const itemPathArr = itemPath.split(':').slice(0, -1).map(el => el + ':');
      return {
        path: itemPath,
        title: this._flatStructure.find(({path}) => path === itemPath)?.title || '',
        name: this.getTextWithoutLastColon(itemPathArr.pop()),
        isActive: itemPath === activePath
      };
    });
  }

  private convertToFlatStructure( structure ): ExtendedEntity[] {
    if (!structure) {
      return [];
    }

    const entities: ExtendedEntity[] = [];
    this.convertItem(structure, entities, 0);
    return entities;
  }

  private convertItem( item, result, level, parent? ) {
    const children = item.child ? [...item.child] : [];
    const childrenPaths = children.map(( { path } ) => path);

    const currItem: Entity = new Entity({ ...item, level, parentId: parent?.id, childrenPaths });
    currItem.parentId = parent?.id;
    currItem.getEntityParent = () => this.getEntity(parent?.id);
    result.push(currItem);
    if (children) {
      children.forEach(( child: any ) => this.convertItem(child, result, level + 1, currItem));
    }
  }

  private getEntity( parentId: string ) {
    return parentId && this.entitiesObject[parentId] ? this.entitiesObject[parentId] : null;
  }
}
