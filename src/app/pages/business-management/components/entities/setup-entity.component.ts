import { Component, OnDestroy, ViewEncapsulation } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { SwHubConfigService, SwHubEntityService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { BehaviorSubject, combineLatest, Observable, Subject } from 'rxjs';
import { delay, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';

import { Entity } from '../../../../common/models/entity.model';
import { SetupEntityService } from './setup-entity.service';
import { EntityService } from '../../../../common/services/entity.service';
import { EntityStateService } from '../../entity-state.service';

export interface Tab {
  name: string;
  displayName: string;
  available?: boolean;
}

@Component({
    selector: 'setup-entity',
    templateUrl: './setup-entity.component.html',
    styleUrls: ['./setup-entity.component.scss'],
    encapsulation: ViewEncapsulation.None,
    providers: [
        SetupEntityService,
    ],
    standalone: false
})
export class SetupEntityComponent implements OnDestroy {
  activeTab: string;

  tabs$: Observable<Tab[]>;
  tabs: Tab[];
  loading$ = new BehaviorSubject<boolean>(false);

  private entity$ = new BehaviorSubject<Entity>(undefined);
  private tabHash: { [name: string]: Tab };
  private readonly destroyed$ = new Subject<void>();

  get entity(): Entity {
    return this.entity$.value;
  }

  constructor(private activatedRoute: ActivatedRoute,
              private router: Router,
              private notifications: SwuiNotificationsService,
              private setupEntityService: SetupEntityService,
              private hubConfigService: SwHubConfigService,
              private titleService: Title,
              private entityStateService: EntityStateService,
              private readonly swHubEntityService: SwHubEntityService,
              entityService: EntityService<Entity>
  ) {
    this.setupEntityService.initSnapshot(this.activatedRoute.snapshot.data);
    this.entity$.next(this.setupEntityService.entity);
    this.tabs$ = this.entityStateService.tabs$;
    this.entityStateService.tabs$
      .pipe(
        switchMap(tabs => {
          this.tabs = tabs;
          this.createTabHash();
          return this.router.events;
        }),
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.setActiveTab();
      });

    this.setupEntityService.entityNeedUpdate$
      .pipe(
        switchMap(() => entityService.getItem(this.setupEntityService.entity.path)),
        takeUntil(this.destroyed$)
      )
      .subscribe(entity => {
        this.setupEntityService.initSnapshot({...this.activatedRoute.snapshot.data, entity});
        this.entity$.next(this.setupEntityService.entity);
      });

    this.activatedRoute.params
      .pipe(
        tap(() => this.loading$.next(true)),
        delay(0),
        takeUntil(this.destroyed$)
      )
      .subscribe(() => {
        this.setupEntityService.initSnapshot(this.activatedRoute.snapshot.data);
        this.entity$.next(this.setupEntityService.entity);
        this.loading$.next(false);
      });

    this.entity$
      .pipe(takeUntil(this.destroyed$))
      .subscribe(entity => {
        this.swHubEntityService.use(entity.id, true);
      });

    combineLatest([this.router.events, this.entity$])
      .pipe(
        filter(([event]) => {
          const fixedUrl = 'business-management/entities/setup';
          if (fixedUrl && event instanceof NavigationEnd) {
            return event?.urlAfterRedirects.includes(fixedUrl);
          }
          return event instanceof NavigationEnd;
        }),
        map(([event, ent]) => {
          let child = this.activatedRoute.firstChild;
          while (child) {
            if (child.firstChild) {
              child = child.firstChild;
            } else if (child.snapshot.data && child.snapshot.data['title']) {
              return [child.snapshot.data['title'], ent, event];
            } else {
              return ['', ent, event];
            }
          }
          return ['', ent, event];
        }),
        takeUntil(this.destroyed$)
      )
      .subscribe(([title, entity]) => {
        const hubName = 'Casino';
        const additionalTitle = this.entity ? `Edit ${entity.title || entity.name} -` : '';
        const envName = this.hubConfigService.envName?.toLocaleUpperCase();
        const location = this.hubConfigService.locationName?.toLocaleUpperCase();
        const processedHubName = hubName ? `${hubName} | ` : '';
        this.titleService.setTitle(`UBO ${location || ''} ${envName || ''} | ${processedHubName} ${additionalTitle || ''} ${title}`);
      });
  }

  ngOnDestroy(): void {
    this.entityStateService.resetEntity();
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  gotoTab(tab: Tab, event?: MouseEvent): void {
    if (event) {
      event.preventDefault();
    }

    this.router.navigate([tab.name], {relativeTo: this.activatedRoute, fragment: tab.name})
      .then(
        (isNavigated: boolean) => {
          if (isNavigated) {
            this.activeTab = tab.name;
          }
        },
        err => {
          console.log(err);
          this.notifications.error(`Unable to load tab "${tab.name}". Please contact customer support`);
        }
      );
  }

  private setActiveTab() {
    let tabName = this.activatedRoute.snapshot.fragment || this.activeTab;
    const isAvailable = tabName && tabName in this.tabHash && this.tabHash[tabName].available;
    const tab = isAvailable ? this.tabHash[tabName] : this.tabs.find(t => t.available);

    if (tab) {
      this.activeTab = tab.name;

      if (!this.activatedRoute.snapshot.fragment) {
        this.gotoTab(tab);
      }
    } else {
      this.notifications.error('Sorry, there are no available tabs for you. Please contact customer support');
    }
  }

  private createTabHash() {
    this.tabHash = this.tabs.reduce((hash, tab) => {
      hash[tab.name] = tab;
      return hash;
    }, {});
  }
}
