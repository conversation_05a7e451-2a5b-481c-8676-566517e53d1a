import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Site } from '../../../../../../../common/models/site.model';

interface RemoveSiteConfirmDialogData {
  rows: Site[];
}

@Component({
    selector: 'remove-confirm-dialog',
    templateUrl: 'remove-confirm-dialog.component.html',
    standalone: false
})
export class RemoveConfirmDialogComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: RemoveSiteConfirmDialogData,
    private dialogRef: MatDialogRef<RemoveConfirmDialogComponent, Site[]>,
  ) {
  }

  ngOnInit() {
  }

  doConfirm() {
    this.dialogRef.close(this.data.rows);
  }
}
