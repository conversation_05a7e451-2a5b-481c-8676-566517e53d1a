import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionModel, BulkAction, SWUI_GRID_SELECTION_TRANSFORMER_TOKEN, SwuiGridComponent, SwuiGridField, SwuiNotificationsService
} from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, finalize, switchMap, switchMapTo, take, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../../common/models/entity.model';
import { Site } from '../../../../../../common/models/site.model';
import { SiteService } from '../../../../../../common/services/site.service';
import { EditSiteDialogComponent } from './dialogs/edit-site-dialog.component';
import { RemoveConfirmDialogComponent } from './dialogs/remove-confirm-dialog.component';
import { SCHEMA_LIST } from './sites.schema';


export function selectionTransformer( item ) {
  return item.id;
}

@Component({
    selector: 'entity-sites-available, [entity-sites-available]',
    templateUrl: './entity-sites-available.component.html',
    styleUrls: ['entity-sites-available.component.scss'],
    providers: [
        {
            provide: SWUI_GRID_SELECTION_TRANSFORMER_TOKEN, useValue: {
                transform: selectionTransformer
            }
        }
    ],
    standalone: false
})

export class EntitySitesAvailableComponent implements OnInit {

  @Input() entity: Entity;
  @Input() settings: EntitySettingsModel | { useSiteAuthorization: boolean } = { useSiteAuthorization: false };

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<Site, string>;

  sites: Site[];

  schemaItems: SwuiGridField[];
  rowActions = [
    new ActionModel({
      icon: 'create',
      title: 'ENTITY_SETUP.WHITELISTING.actionEdit',
      fn: this.onEditSiteAction.bind(this)
    }),
    new ActionModel({
      icon: 'delete',
      title: 'ENTITY_SETUP.WHITELISTING.actionRemove',
      fn: ( site ) => this.confirmSingleSiteDelete(site)
    }),
  ];
  _filteredSchema: SwuiGridField[];

  loading = true;

  bulkActions: BulkAction[] = [
    new BulkAction({
      title: 'ENTITY_SETUP.WHITELISTING.activate',
      fn: () => this.performBulkAction('activate')
    }),
    new BulkAction({
      title: 'ENTITY_SETUP.WHITELISTING.deactivate',
      fn: () => this.performBulkAction('deactivate')
    }),
    new BulkAction({
      title: 'ENTITY_SETUP.WHITELISTING.btnRemoveSelected',
      fn: () => this.performBulkAction('remove')
    })
  ];

  private readonly destroyed$ = new Subject<void>();
  private _schema: SwuiGridField[];

  get schema(): SwuiGridField[] {
    return this._schema;
  }

  set schema( values: SwuiGridField[] ) {
    this._filteredSchema = values.filter(this.isCellVisible.bind(this));
    this._schema = values;
  }

  constructor( private siteService: SiteService<Site>,
               private notificationService: SwuiNotificationsService,
               private translateService: TranslateService,
               private dialog: MatDialog,
  ) {
    this.subscribeSitesUpdate();
    this.setupGrid();
  }

  ngOnInit() {
    this.loadSiteData();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  subscribeSitesUpdate(): void {
    this.siteService.items
      .pipe(takeUntil(this.destroyed$))
      .subscribe(response => this.sites = response as Site[]);
  }

  setupGrid(): void {
    this.schema = SCHEMA_LIST;
    this.schemaItems = this.schema.filter(item => item.isList);
  }

  isCellVisible( item: SwuiGridField ): boolean {
    if (item) {
      if (item.isList === true) {
        if (typeof item.isListVisible !== 'undefined') {
          return item.isListVisible;
        }
      }
      return false;
    }
    return (item && item.isListVisible) || (item && item.isList);
  }

  onEditSiteAction( site: Site ): void {
    this.dialog.open(EditSiteDialogComponent, {
      disableClose: true,
      width: '600px',
      data: { site }
    }).afterClosed()
      .pipe(
        take(1),
        filter(( data ) => !!data)
      ).subscribe(( changed ) => this.onSiteSaved(changed));
  }

  showBulkAddSiteModal(): void {
    this.dialog.open(EditSiteDialogComponent, {
      disableClose: true,
      width: '600px',
      data: { entity: this.entity }
    }).afterClosed().pipe(
      filter(( data ) => !!data),
      switchMap(site => this.siteService.addAvailableSite(site, this.entity.path)),
      tap(() => this.notificationService.success(
        this.translateService.instant('ENTITY_SETUP.WHITELISTING.notificationAdded'))),
      take(1),
    ).subscribe(() => this.siteService.getAvailableSites(this.entity.path));
  }

  onSiteSaved( site: Site ): void {
    let siteModified: boolean = !!site.id;
    let saveSource$ = siteModified
      ? this.siteService.modifyAvailableSite(site, this.entity.path)
      : this.siteService.addAvailableSite(site, this.entity.path);

    saveSource$
      .pipe(
        finalize(() => this.siteService.getAvailableSites(this.entity.path)),
        switchMapTo(this.translateService.get('ENTITY_SETUP.WHITELISTING.notificationSaved')),
        take(1)
      )
      .subscribe(( message ) => this.notificationService.success(message, ''));
  }

  onWidgetActionFn( config ): void {
    if (config?.eventType === 'radio') {
      this.setAsDefault(config.event, config.row);
    } else {
      const { field, row, payload } = config;
      switch (field) {
        case 'status':
          let update = <Site>Object.assign({}, row, { status: payload.status });
          let sub = this.siteService.modifyAvailableSite(update, this.entity.path).subscribe(
            ( response ) => {
              if ('status' in response) {
                row.status = response.status;
              }
              payload.onCompleteFn();
              sub.unsubscribe();
            }
          );
          break;

        default:
          break;
      }
    }
  }

  setAsDefault( event: MouseEvent, site: Site ) {
    if (!site.isDefault) {
      event.preventDefault();

      this.dialog.open(BoConfirmationComponent, {
        width: '500px',
        data: {
          message: this.translateService.instant('ENTITY_SETUP.WHITELISTING.MODALS.confirmSetAsDefault', { url: site.url }),
        },
        disableClose: true
      }).afterClosed()
        .pipe(
          take(1),
          filter(isConfirmed => isConfirmed),
          switchMap(() => this.siteService.modifyAvailableSite({ ...site, ...{ isDefault: true } }, this.entity.path))
        )
        .subscribe(
          () => {
            this.notificationService.success(
              this.translateService.instant('ENTITY_SETUP.WHITELISTING.MODALS.successSetAsDefault', { url: site.url }));

            this.grid.selection.clear();
            this.loadSiteData();
          }
        );
    }
  }

  confirmSingleSiteDelete( site: Site ): void {
    this.dialog.open(RemoveConfirmDialogComponent, {
      width: '600px',
      data: { rows: [site] },
      disableClose: true
    }).afterClosed().pipe(
      filter(( sites ) => !!sites),
      take(1),
    ).subscribe(( [confirmed] ) => this.removeSite(confirmed));
  }

  private removeSite( { id }: Site ): void {
    this.siteService.removeAvailableSite(id, this.entity.path)
      .pipe(
        finalize(() => this.siteService.getAvailableSites(this.entity.path)),
        switchMapTo(this.translateService.get('ENTITY_SETUP.WHITELISTING.notificationRemoved')),
        take(1)
      )
      .subscribe(message => this.notificationService.success(message, ''));
  }

  private performBulkAction( action: string ) {
    const sites: Site[] = this.grid &&
      this.grid.dataSource.data.filter(site => this.grid.selection.selected.indexOf(site.id) > -1);

    const body = [{ sites: sites.map(( { url } ) => url), action }];

    this.siteService.changeSiteStatus(this.entity.path, body).pipe(
      finalize(() => {
        this.grid.selection.clear();
        this.loadSiteData();
      }),
      switchMap(() => this.translateService.get('ENTITY_SETUP.WHITELISTING.notificationChanged')),
      tap(( message ) => this.notificationService.success(message, '')),
      take(1)
    ).subscribe();
  }

  private loadSiteData() {
    this.siteService.getAvailableSites(this.entity.path)
      .pipe(take(1))
      .subscribe(( sites: Site[] ) => {
        this.sites = sites;
        this.loading = false;
      });
  }
}
