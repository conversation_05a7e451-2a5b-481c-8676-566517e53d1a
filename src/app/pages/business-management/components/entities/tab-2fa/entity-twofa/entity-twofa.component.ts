import { Component, Input } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
  EntitySettingsModel, MailTemplatesList, SmsTemplatesList, TWOFA_TYPE_STATES_TRANSLATE,
} from '../../../../../../common/models/entity-settings.model';

import { Entity } from '../../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';
import { finalize } from 'rxjs/operators';


@Component({
    selector: 'entity-twofa',
    templateUrl: './entity-twofa.component.html',
    styleUrls: ['./entity-twofa.component.scss'],
    standalone: false
})

export class EntityTwofaComponent {

  public toggleLoading: boolean = false;
  public twoFADisabled: boolean = false;
  public isMailGroupActive: boolean = false;
  public isSmsGroupActive: boolean = false;
  public authOptionsForm: FormGroup;
  public isMailFormValid: boolean;
  public isSmsFormValid: boolean;

  private _mailTemplate: MailTemplatesList;
  private _smsTemplate: SmsTemplatesList;
  private _entity: Entity;
  private _settings: EntitySettingsModel;

  @Input()
  public set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
  }

  public get entity(): Entity {
    return this._entity;
  }

  @Input()
  public set settings( value: EntitySettingsModel ) {
    if (!value) return;
    this._settings = value;
    this.setFormValues();
    this.updateTwoFAStatus(value);
  }

  public get settings(): EntitySettingsModel {
    return this._settings;
  }

  constructor( private fb: FormBuilder,
               private service: EntitySettingsService<EntitySettingsModel>,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
  ) {
    this.initForm();

    this.handleSmsActivated();
    this.handleEmailActivated();
  }

  public toggleTwoFAStatus( event ) {
    event.preventDefault();
    this.toggleLoading = true;

    let twoFAWillBeDisabled = !this.twoFADisabled;
    let saveChanges$: Observable<EntitySettingsModel> = twoFAWillBeDisabled
      ? this.service.disableTwoFAByPath(this.entity.path)
      : this.service.enableTwoFAByPath(this.entity.path);

    saveChanges$
      .pipe(
        tap(( response: EntitySettingsModel ) => {
          this.settings = new EntitySettingsModel(response);
          this.updateTwoFAStatus(this.settings);
          this.closeOpenTemplatesGroups();
        }),
        tap(() => {
          let action: string = this.settings.twoFactorAuthSettings.isAuthEnabled ? 'activated' : 'deactivated';
          let notification = action === 'activated' ?
            'ENTITY_SETUP.TWOFA_SETUP.NOTIFICATIONS.activated' :
            'ENTITY_SETUP.TWOFA_SETUP.NOTIFICATIONS.deactivated';
          this.notifications.success(this.translate.instant(notification));
        }),
        finalize(() => this.toggleLoading = false),
      ).subscribe();
  }

  public handleOpenChangeMails($event) {
    this.isMailGroupActive = $event;
  }

  public handleOpenChangeSms($event) {
    this.isSmsGroupActive = $event;
  }

  public handleMailTemplateFormValue(data: MailTemplatesList): void {
    this._mailTemplate = data;
  }

  public handleSmsTemplateFormValue(data: SmsTemplatesList): void {
    this._smsTemplate = data;
  }

  public handleIsSmsFormValid(value: boolean) {
    this.isSmsFormValid = value;
  }

  public handleIsMailFormValid(value: boolean) {
    this.isMailFormValid = value;
  }

  public save2FASettings( event: Event ) {
    event.preventDefault();
    let { google, sms, email } = this.authOptionsForm.value;

    if (this.isMailFormValid && this.isSmsFormValid) {
      if (email) {
        this.settings.setEmailTemplate(this._mailTemplate);
      }
      if (sms) {
        this.settings.setSmsTemplate(this._smsTemplate);
      }
      this.settings.update2FAMethods({ google, sms, email });

      const allOptionsDisabled = Object
        .keys(this.authOptionsForm.controls)
        .every(key => this.authOptionsForm.get(key).value === false);

      if (allOptionsDisabled) {
        this.settings.twoFactorAuthSettings.isAuthEnabled = false;
      }
      this.service.patchSettings({ twoFactorAuthSettings: this.settings.twoFactorAuthSettings }, this.entity.path)
        .subscribe(( updatedSettings ) => {
          this.settings = new EntitySettingsModel(updatedSettings);
          this.translate.get('ENTITY_SETUP.TWOFA_SETUP.NOTIFICATIONS.saved')
            .subscribe(( message ) => this.notifications.success(message));
        });
    }
  }

  public getTypeStates(): string[] {
    return TWOFA_TYPE_STATES_TRANSLATE.map(stateName => this.translate.instant(stateName));
  }

  public initForm() {
    this.authOptionsForm = this.fb.group({
      'google': '',
      'sms': '',
      'email': '',
    });
  }

  private closeOpenTemplatesGroups() {
    if (!this.settings.twoFactorAuthSettings.isAuthEnabled) {
      this.isMailGroupActive = false;
      this.isSmsGroupActive = false;
    }
  }

  private updateTwoFAStatus( value: EntitySettingsModel ) {
    this.twoFADisabled = value.twoFactorAuthSettings.isAuthEnabled === false;

    if (this.twoFADisabled) {
      this.disableForm();
    } else {
      this.enableForm();
    }
  }

  private disableForm() {
    this.authOptionsForm.patchValue({
      'google': '',
      'sms': '',
      'email': '',
    });
    this.authOptionsForm.disable();
  }

  private enableForm() {
    this.authOptionsForm.enable();
  }

  private setFormValues() {
    this.authOptionsForm.patchValue({
      'google': this.settings.isSpecific2FAEnabled('google'),
      'sms': this.settings.isSpecific2FAEnabled('sms'),
      'email': this.settings.isSpecific2FAEnabled('email'),
    });
  }

  private handleEmailActivated(): void {
    this.authOptionsForm.get('email').valueChanges.subscribe(() => {
      this.isMailGroupActive = this.authOptionsForm.get('email').value;
    });
  }

  private handleSmsActivated(): void {
    this.authOptionsForm.get('sms').valueChanges.subscribe(() => {
      this.isSmsGroupActive = this.authOptionsForm.get('sms').value;
    });
  }
}
