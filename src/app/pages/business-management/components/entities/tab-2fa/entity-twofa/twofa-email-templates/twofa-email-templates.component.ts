import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';

import {
  EntitySettingsModel, handleFormUseDefault, MailTemplatesList, processTemplate, TWOFA_AVAILABLE_LANGUAGES,
} from '../../../../../../../common/models/entity-settings.model';


@Component({
    selector: 'twofa-email-templates',
    templateUrl: './twofa-email-templates.component.html',
    standalone: false
})

export class TwofaEmailTemplatesComponent {

  @Output() formValue: EventEmitter<MailTemplatesList> = new EventEmitter();
  @Output() isValid: EventEmitter<boolean> = new EventEmitter();

  public mailTemplateForm: FormGroup;
  public languages: { key: string, title: string }[] = TWOFA_AVAILABLE_LANGUAGES;

  private _settings: EntitySettingsModel;

  @Input()
  public set settings( value: EntitySettingsModel ) {
    if (!value) return;
    this._settings = value;
    this.setMailTemplateForm(this._settings.twoFactorAuthSettings.mailTemplates);
  }

  @Input()
  public set isActivated ( value: boolean ) {
    if (!value && this._settings){
      this.setMailTemplateForm(this._settings.twoFactorAuthSettings.mailTemplates);
    }
  }

  public get settings(): EntitySettingsModel {
    return this._settings;
  }

  constructor( private fb: FormBuilder) {
    this.mailTemplateForm = this.initMailTemplateForm();
    handleFormUseDefault(this.mailTemplateForm);
    this.mailTemplateForm.valueChanges.subscribe(data => {
      this.isValid.emit(this.mailTemplateForm.valid);
      this.formValue.emit(processTemplate(data));
    });
  }

  public initMailTemplateForm(): FormGroup {
    const mailTemplateForm = this.fb.group({});
    this.languages.map(el => {
      mailTemplateForm.addControl(el.key, this.initMailTemplateItem());
    });
    return mailTemplateForm;
  }


  private initMailTemplateItem(): FormGroup {
    return this.fb.group({
      from: ['', Validators.required],
      subject: ['', Validators.required],
      html: ['', Validators.required],
      useDefault: false
    });
  }

  private setMailTemplateForm(mailTemplates: MailTemplatesList): void {
    this.mailTemplateForm.patchValue(mailTemplates);
    this.languages.map(lang => {
      if (this._settings.twoFactorAuthSettings.mailTemplates &&
        !(lang.key in this._settings.twoFactorAuthSettings.mailTemplates)) {
        this.mailTemplateForm.get(lang.key).reset();
        this.mailTemplateForm.get(lang.key).get('useDefault').setValue(true);
      }
    });
  }

}
