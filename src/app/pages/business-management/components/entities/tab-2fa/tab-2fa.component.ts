import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { delay, filter, switchMap, take, takeUntil } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';

import { Entity } from '../../../../../common/models/entity.model';
import { Site } from '../../../../../common/models/site.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { SiteService } from '../../../../../common/services/site.service';
import { SetupEntityService } from '../setup-entity.service';

export const ENTITY_SITES_AVAILABLE_ANCHOR = 'entity-sites-available-anchor';

@Component({
    selector: 'tab-2fa',
    templateUrl: './tab-2fa.component.html',
    styleUrls: ['./tab-2fa.component.scss'],
    standalone: false
})
export class Tab2FaComponent implements OnDestroy {
  readonly entity?: Entity;
  readonly isSuperadmin: boolean;

  entitySettings?: EntitySettingsModel;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    { isSuperAdmin }: SwHubAuthService,
    { entity }: SetupEntityService,
    private settingsService: EntitySettingsService<EntitySettingsModel>,
    private activatedRoute: ActivatedRoute,
    private siteService: SiteService<Site>,
  ) {
    this.isSuperadmin = isSuperAdmin;
    this.entity = entity;

    this.settingsService.getSettings(this.entity.path)
      .pipe(takeUntil(this.destroyed$))
      .subscribe(response => {
        this.entitySettings = new EntitySettingsModel(response);
      });
    this.scrollToAnchor();
  }

  scrollToAnchor() {
    this.siteService.items.pipe(
      switchMap(() => this.activatedRoute.queryParams),
      filter((queryParams: Params) => {
        const { anchor } = queryParams;
        return !!anchor;
      }),
      delay(500),
      take(1)
    ).subscribe(({ anchor }) => {
      const scrollableContainer = document.getElementById('main-content-wrap');
      const anchorElement = document.getElementById(anchor);

      if (scrollableContainer && anchorElement) {
        scrollableContainer.scroll({
          top: anchorElement.offsetTop + 70,
          behavior: 'smooth'
        });
      }
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
