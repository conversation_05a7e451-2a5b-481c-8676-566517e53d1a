import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { finalize, map, switchMap } from 'rxjs/operators';

import { Entity, UpdateMerchantEntityData } from '../../../../../../common/models/entity.model';
import { SelectOptionModel } from '../../../../../../common/models/select-option.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { GameLogoutOptions } from '../../../../../../common/typings';
import { PERMISSIONS_LIST } from '../../../../../../app.constants';


export const ENTITY_GAME_LOGOUT_TYPE = [
  { id: 'all', text: 'ENTITY_SETUP.ADDITIONAL.all' },
  { id: 'unfinished', text: 'ENTITY_SETUP.ADDITIONAL.unfinished' }
];

@Component({
    selector: 'entity-game-logout',
    templateUrl: './entity-game-logout.component.html',
    styleUrls: ['./entity-game-logout.component.scss'],
    standalone: false
})

export class EntityGameLogoutComponent implements OnInit {

  @Input()
  public set entity( value: Entity ) {
    if (!value) return;
    this._path = value.path;
  }

  public form: FormGroup;
  public submitted: boolean;
  public gameLogoutTypeOptions: SelectOptionModel[];
  public loading: boolean = false;
  public readonly allowedEdit: boolean;

  private _path: string;
  private _merchantEntity: Entity;

  constructor( private fb: FormBuilder,
               private entityService: EntityService<Entity>,
               private translate: TranslateService,
               private notifications: SwuiNotificationsService,
               authService: SwHubAuthService,
  ) {
    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.MERCHANT_EDIT);
    this.gameLogoutTypeOptions = ENTITY_GAME_LOGOUT_TYPE.map(option => Object.assign(option));
    this.form = this.fb.group({
      type: [{ value: '', disabled: !this.allowedEdit }],
      maxRetryAttempts: [{ value: '', disabled: !this.allowedEdit }],
      maxSessionTimeout: [{ value: '', disabled: !this.allowedEdit }],
    });
  }

  ngOnInit() {
    this.entityService.getMerchantEntityItem(this._path)
      .pipe(map(entity => new Entity(entity)))
      .subscribe(( item: Entity ) => {
        this._merchantEntity = item;
        this.patchForm(item);
      });
  }

  onSubmit() {
    this.submitted = true;
    if (this.form.valid && !this.loading) {
      let updateData: UpdateMerchantEntityData = { ...this._merchantEntity.asUpdateMerchantData() };
      updateData.params.gameLogoutOptions = Object.assign( {}, this.processForm(this.form) );
      this.loading = true;
      this.entityService.updateMerchantEntityItem(updateData, this._path)
      .pipe(
        finalize(() => this.loading = false),
        switchMap(() => this.translate.get('ENTITY_SETUP.ADDITIONAL.notificationLogoutOptions'))
      ).subscribe( message => {
          this.notifications.success(message, '');
      });
    }
  }
  private patchForm( entity: Entity ) {
    const gameLogoutOptions = entity.merchant.params.gameLogoutOptions;
    if (gameLogoutOptions) {
      Object.keys(gameLogoutOptions).forEach(key => {
        if (!gameLogoutOptions[key] && gameLogoutOptions[key] !== 'type') {
          gameLogoutOptions[key] = 0;
        }
      });
      this.form.patchValue(gameLogoutOptions);
    }
  }

  private processForm( form: FormGroup ): GameLogoutOptions {
    const options = form.value;
    Object.keys(options).forEach(key => {
      if (options[key] === '' || options[key] === 0) {
        delete options[key];
      }
    });
    return options;
  }
}
