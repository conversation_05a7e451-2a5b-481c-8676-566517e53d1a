import { Component, Input } from '@angular/core';
import { DOMAIN_TYPES, STATIC_DOMAIN_TYPES } from '../../../../../../common/models/domain.model';

import { Entity } from '../../../../../../common/models/entity.model';

@Component({
    selector: 'entity-domains-setup, [entity-domains-setup]',
    templateUrl: './entity-domains.component.html',
    styleUrls: ['./entity-domains.component.scss'],
    standalone: false
})
export class EntityDomainsComponent {
  @Input() entity: Entity;
  public domainTypes = DOMAIN_TYPES;
  public staticDomainTypes = STATIC_DOMAIN_TYPES;
}
