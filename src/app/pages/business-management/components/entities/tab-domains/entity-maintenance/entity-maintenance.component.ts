import { Component, Input } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { filter, switchMap } from 'rxjs/operators';

import { BoConfirmationComponent } from '../../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { EntitySettingsModel } from '../../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../../common/services/entity-settings.service';
import { ValidationService } from '../../../../../../common/services/validation.service';


@Component({
    selector: 'entity-maintenance',
    templateUrl: './entity-maintenance.component.html',
    styleUrls: ['./entity-maintenance.component.scss'],
    standalone: false
})

export class EntityMaintenanceComponent {

  public form: FormGroup;

  private _entity: Entity;
  private _settings: EntitySettingsModel;

  @Input()
  public set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
    this.getSettings(value.path);
  }

  public get entity(): Entity {
    return this._entity;
  }

  constructor( private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
               private notificationService: SwuiNotificationsService,
               private translate: TranslateService,
               private fb: FormBuilder,
               private dialog: MatDialog,
  ) {
    this.form = this.initForm();
  }

  public isCurrentUrl(): boolean {
    if (this._settings) {
      return this._settings.maintenanceUrl === this.form.get('maintenanceUrl').value;
    }
  }

  showConfirmation( event: Event ) {
    event.preventDefault();
    if (this.form.valid && !this.isCurrentUrl()) {
      this.dialog.open(BoConfirmationComponent, {
        width: '600px',
        data: { message: 'ENTITY_SETUP.DOMAINS.maintenanceUrlConfirmation' },
        disableClose: true
      }).afterClosed()
        .pipe(
          filter(val => !!val),
          switchMap(() => {
            const result: EntitySettingsModel = Object.assign({}, this.form.value);
            return result.maintenanceUrl === '' ?
              this.entitySettingsService.resetSettingsToDefault({ maintenanceUrl: this._settings.maintenanceUrl }, this.entity.path) :
              this.entitySettingsService.patchSettings(result, this.entity.path);
          }),
          switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.maintenanceUrlEditSuccess'))
        )
        .subscribe(( message: string ) => {
          this.getSettings(this.entity.path);
          this.notificationService.success(message, '');
        });
    }
  }

  private initForm(): FormGroup {
    return this.fb.group({
      maintenanceUrl: ['', ValidationService.urlValidation],
    });
  }

  private getSettings( entity ) {
    this.entitySettingsService.getSettings(entity)
      .subscribe(data => {
        this._settings = data;
        this.setMaintenanceUrl(data);
      });
  }

  private setMaintenanceUrl( settings: EntitySettingsModel ): void {
    if (settings.maintenanceUrl === null || settings.maintenanceUrl === undefined) {
      settings.maintenanceUrl = '';
    }
    this.form.get('maintenanceUrl').setValue(settings.maintenanceUrl);
  }
}
