import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { first, switchMap } from 'rxjs/operators';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { TranslateService } from '@ngx-translate/core';
import { Entity } from 'src/app/common/typings';
import { EntitySettingsService } from 'src/app/common/services/entity-settings.service';
import { DomainsManagementService } from 'src/app/pages/domains-management/domains-management.service';
import { Domain, DOMAIN_TYPES } from 'src/app/common/models/domain.model';
import { EntitySettingsModel } from 'src/app/common/models/entity-settings.model';

@Component({
    selector: 'entity-allowed-child-domains',
    templateUrl: './entity-allowed-child-domains.component.html',
    styleUrls: ['./entity-allowed-child-domains.component.scss'],
    standalone: false
})
export class EntityAllowedChildDomainsComponent implements OnInit {
  @Input() entity: Entity;

  readonly form: FormGroup;
  readonly domainsControl = new FormControl([]);
  availableDomains: Domain[] = [];
  selectedDomainIds: string[] = [];
  currentSettings: EntitySettingsModel;

  constructor(
    fb: FormBuilder,
    private readonly notifications: SwuiNotificationsService,
    private readonly translate: TranslateService,
    private readonly entitySettingsService: EntitySettingsService<EntitySettingsModel>,
    private readonly domainsManagementService: DomainsManagementService,
  ) {
    this.form = fb.group({
      domains: this.domainsControl,
    });
  }

  ngOnInit() {
    this.domainsManagementService.getList(DOMAIN_TYPES.static)
      .pipe(first())
      .subscribe((domains: Domain[]) => {
        this.availableDomains = domains;
      });

    this.entitySettingsService.getSettings(this.entity.path)
      .pipe(first())
      .subscribe((settings: EntitySettingsModel) => {
        this.currentSettings = settings;
        if (settings?.allowedStaticDomainsForChildId) {
          this.selectedDomainIds = [...settings.allowedStaticDomainsForChildId];
          this.domainsControl.setValue(this.selectedDomainIds);
        }
      });
  }

  onDomainSelectionChange(selectedIds: string[]) {
    this.selectedDomainIds = selectedIds;
    this.domainsControl.setValue(selectedIds);
  }

  saveDomains() {
    const domainIds = this.domainsControl.value || [];
    const settingsUpdate = { allowedStaticDomainsForChildId: domainIds };

    this.entitySettingsService.patchSettings(settingsUpdate, this.entity.path)
      .pipe(
        first(),
        switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsSaved'))
      )
      .subscribe(msg => {
        this.notifications.success(msg);
        if (this.currentSettings) {
          this.currentSettings.allowedStaticDomainsForChildId = domainIds;
        }
      });
  }

  resetDomains() {
    const settingsUpdate = { allowedStaticDomainsForChildId: [] };
    this.entitySettingsService.patchSettings(settingsUpdate, this.entity.path)
      .pipe(
        first(),
        switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsReset'))
      )
      .subscribe((msg: string) => {
        this.domainsControl.setValue([]);
        this.selectedDomainIds = [];
        this.notifications.success(msg);
        if (this.currentSettings) {
          this.currentSettings.allowedStaticDomainsForChildId = [];
        }
      });
  }

  getDomainDisplayName(domainId: string): string {
    const domain = this.availableDomains.find(d => d.id === domainId);
    return domain ? domain.domain : domainId;
  }
}
