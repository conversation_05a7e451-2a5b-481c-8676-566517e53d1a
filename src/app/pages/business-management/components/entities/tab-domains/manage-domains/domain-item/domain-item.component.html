<ng-template #emptyDomain>
  <strong class="text-warning">{{'ENTITY_SETUP.DOMAINS.notSet' | translate}}</strong>
</ng-template>

<ng-template #loadingSpinner>
  {{ 'COMPONENTS.GRID.LOADING' | translate }}
</ng-template>

<div class="domain">
  <div class="domain--info">
    <span>{{ 'ENTITY_SETUP.DOMAINS.' + domainType + (staticDomainType ? '.' + staticDomainType : '') | translate}}:</span>&nbsp;
    <ng-container *ngIf="!loading; else loadingSpinner">
      <ng-container *ngIf="domain?.id; else emptyDomain">
        <strong>{{ domain.domain }}</strong>
        <span *ngIf="domainType === domainTypes.dynamic" class="ml-5">({{'ENTITY_SETUP.DOMAINS.environment' | translate}}: {{ domain.environment }})</span>
        <span *ngIf="domain.inherited" class="domain-inherited ml-5">({{'ENTITY_SETUP.DOMAINS.inherited' | translate}})</span>
      </ng-container>
    </ng-container>
  </div>
  <div class="domain--controls">
    <button mat-icon-button (click)="setNewDomain()" matTooltip="{{'ENTITY_SETUP.DOMAINS.set' | translate}}">
      <mat-icon>edit</mat-icon>
    </button>
    <button mat-icon-button [disabled]="resetButtonDisabled()" matTooltip="{{'ENTITY_SETUP.DOMAINS.reset' | translate}}"
            (click)="resetToParent($event)">
      <mat-icon>replay</mat-icon>
    </button>
  </div>

</div>
