import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSelectModule } from '@skywind-group/lib-swui';
import { EntitySettingsService } from 'src/app/common/services/entity-settings.service';
import { DomainsPoolService } from '../../../../../domains-management/domains-pool/domains-pool.service';
import { EntityAllowedChildDomainsComponent } from './allowed-child-domains/entity-allowed-child-domains.component';
import { DomainItemComponent } from './domain-item/domain-item.component';
import { EntityDomainPoolService } from './entity-domain-pool.service';
import { PoolItemComponent } from './pool-item/pool-item.component';
import { SelectDomainDialogComponent } from './select-domain-dialog.component';
import { SelectPoolDialogComponent } from './select-pool-dialog/select-pool-dialog.component';
import { EntityStaticDomainTagsComponent } from './static-tags/entity-static-domain-tags.component';
import { DomainsManagementService } from 'src/app/pages/domains-management/domains-management.service';
import { EntityDomainService } from 'src/app/pages/domains-management/entity-domain.service';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatChipsModule,
    MatSelectModule,
    SwuiSelectModule,
  ],
  exports: [
    DomainItemComponent,
    PoolItemComponent,
    EntityStaticDomainTagsComponent,
    EntityAllowedChildDomainsComponent,
  ],
  declarations: [
    SelectDomainDialogComponent,
    SelectPoolDialogComponent,
    DomainItemComponent,
    PoolItemComponent,
    EntityStaticDomainTagsComponent,
    EntityAllowedChildDomainsComponent,
  ],
  providers: [
    DomainsManagementService,
    DomainsPoolService,
    EntityDomainService,
    EntityDomainPoolService,
    EntitySettingsService
  ]
})
export class ManageDomainsModule {
}
