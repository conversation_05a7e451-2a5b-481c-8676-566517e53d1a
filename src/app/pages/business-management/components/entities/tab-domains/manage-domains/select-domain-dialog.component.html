<h2 mat-dialog-title>
  {{ 'ENTITY_SETUP.DOMAINS.modalTitle' | translate: { type: domainType } }}
</h2>
<mat-dialog-content class="mat-typography">
  <ng-container *ngIf="!loading; else loadingSpinner">
    <mat-form-field appearance="outline" style="width: 100%">
      <mat-label>{{'ENTITY_SETUP.DOMAINS.domain' | translate}}</mat-label>
      <lib-swui-select [data]="selectOptions" [formControl]="domainControl" [showSearch]="true"></lib-swui-select>
    </mat-form-field>
  </ng-container>
  <ng-template #loadingSpinner>
    <div class="loading-container"
      style="display: flex; justify-content: center; align-items: center; min-height: 100px;">
      <mat-spinner diameter="40"></mat-spinner>
    </div>
  </ng-template>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{'DIALOG.cancel' | translate}}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial [class.disabled]="!selectedDomain"
    [disabled]="!selectedDomain" (click)="submitSelectedDomain()">
    {{'DIALOG.save' | translate}}
  </button>
</mat-dialog-actions>
