import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { finalize, take, takeUntil } from 'rxjs/operators';
import { DomainsManagementService } from '../../../../../domains-management/domains-management.service';
import { Domain, DomainType, StaticDomainType } from '../../../../../../common/models/domain.model';

export interface SelectDomainDialogData {
  domainType: DomainType;
  staticDomainType?: StaticDomainType;
  domainId: string;
}

@Component({
    selector: 'select-domain-dialog',
    templateUrl: 'select-domain-dialog.component.html',
    standalone: false
})
export class SelectDomainDialogComponent implements OnInit, On<PERSON><PERSON>roy {
  readonly domainControl: FormControl;
  readonly domainType: DomainType;

  selectOptions: SwuiSelectOption[] = [];
  selectedDomain: Domain;
  loading = true;

  private readonly staticDomainType?: StaticDomainType;
  private readonly domainId: string;
  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly dialogRef: MatDialogRef<SelectDomainDialogComponent, Domain>,
    @Inject(MAT_DIALOG_DATA) { domainType, staticDomainType, domainId }: SelectDomainDialogData,
    private readonly service: DomainsManagementService,
  ) {
    this.domainType = domainType;
    this.staticDomainType = staticDomainType;
    this.domainControl = new FormControl(domainId ?? '');
    this.domainId = domainId;
  }

  ngOnInit() {
    this.service.getList(this.domainType).pipe(
      finalize(() => this.loading = false),
      take(1)
    ).subscribe((domains) => {
      if (this.staticDomainType) {
        domains = domains.filter(({ type }) => type === this.staticDomainType);
      } else {
        domains = domains;
      }

      this.selectOptions = domains.map(({ id, domain, environment }) => ({
        id,
        text: `${domain}${environment ? ` (${environment})` : ''}`,
        disabled: id === this.domainId
      }));

      this.domainControl.valueChanges.pipe(
        takeUntil(this.destroyed$)
      ).subscribe((id) => {
        this.selectedDomain = domains.find(domain => domain.id === id);
      });
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  submitSelectedDomain() {
    if (this.selectedDomain) {
      this.dialogRef.close(this.selectedDomain);
    }
  }
}
