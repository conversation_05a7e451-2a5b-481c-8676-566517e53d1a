import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiSelectOption } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DomainPool } from '../../../../../../../common/models/domain.model';

export interface SelectPoolDialogData {
  domainPool: DomainPool;
  domainPools: DomainPool[];
}

@Component({
    selector: 'select--pool-dialog',
    templateUrl: 'select-pool-dialog.component.html',
    standalone: false
})
export class SelectPoolDialogComponent implements OnInit, OnDestroy {
  readonly selectOptions: SwuiSelectOption[] = [];
  selected: DomainPool;
  poolControl = new FormControl();

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly dialogRef: MatDialogRef<SelectPoolDialogComponent, DomainPool>,
    @Inject(MAT_DIALOG_DATA) private readonly data: SelectPoolDialogData,
  ) {
    this.poolControl.setValue(data.domainPool?.id ?? '');
    this.selectOptions = data.domainPools?.map((item) => ({
      id: item.id,
      text: item.name
    })) ?? [];
  }

  ngOnInit() {
    this.poolControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe((id: string) => {
        this.selected = this.data.domainPools?.find(pool => pool.id === id);
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  submitSelected() {
    if (this.selected) {
      this.dialogRef.close(this.selected);
    }
  }
}
