import { Component, Input } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { first } from 'rxjs/operators';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { TranslateService } from '@ngx-translate/core';
import { Entity } from 'src/app/common/typings';
import { EntityDomainService } from 'src/app/pages/domains-management/entity-domain.service';

@Component({
    selector: 'entity-static-domain-tags',
    templateUrl: './entity-static-domain-tags.component.html',
    standalone: false
})
export class EntityStaticDomainTagsComponent {
  @Input() entity: Entity;

  readonly separatorKeys = [13, 188];
  readonly form: FormGroup;
  readonly tagsControl = new FormControl([]);

  constructor(
    fb: FormBuilder,
    private readonly notifications: SwuiNotificationsService,
    private readonly translate: TranslateService,
    private readonly entityDomainService: EntityDomainService,
  ) {
    this.form = fb.group({
      tags: this.tagsControl,
    });
  }

  onChipAdd(event: any) {
    const inputValue: string = (event?.value || '').trim();
    if (!inputValue) {
      return;
    }
    const current = this.tagsControl.value || [];
    if (current.indexOf(inputValue) === -1) {
      this.tagsControl.setValue([...current, inputValue]);
    }
    if (event?.chipInput?.clear) {
      event.chipInput.clear();
    }
  }

  removeTag(index: number) {
    const current: string[] = this.tagsControl.value || [];
    this.tagsControl.setValue(current.filter((_val: string, i: number) => i !== index));
  }

  setTags() {
    const tags = this.tagsControl.value || [];
    this.entityDomainService.setStaticDomainTags(this.entity.path, tags)
      .pipe(first())
      .subscribe(() => {
        this.translate.get('ENTITY_SETUP.DOMAINS.tagsSaved').pipe(first()).subscribe(msg => this.notifications.success(msg));
      });
  }

  resetTags() {
    this.entityDomainService.resetStaticDomainTags(this.entity.path)
      .pipe(first())
      .subscribe(() => {
        this.tagsControl.setValue([]);
        this.translate.get('ENTITY_SETUP.DOMAINS.tagsReset').pipe(first()).subscribe(msg => this.notifications.success(msg));
      });
  }
}
