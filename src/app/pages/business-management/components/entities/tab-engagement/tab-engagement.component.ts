import { Component } from '@angular/core';

import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { SetupEntityService } from '../setup-entity.service';


@Component({
    selector: 'tab-rates',
    templateUrl: 'tab-engagement.component.html',
    standalone: false
})
export class TabEngagementComponent {
  readonly entitySettings?: EntitySettingsModel;
  readonly entity?: Entity;

  constructor( { entity, settings }: SetupEntityService ) {
    this.entity = entity;
    this.entitySettings = settings;
  }
}
