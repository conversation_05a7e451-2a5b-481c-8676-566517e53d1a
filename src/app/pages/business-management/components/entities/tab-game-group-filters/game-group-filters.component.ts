import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  fromGameInfo, GameSelectItem, RowAction, SelectInputOptionData, SwuiGridComponent, SwuiGridDataService, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Entity } from '../../../../../common/models/entity.model';
import { GameGroup, GameGroupFilter } from '../../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../../common/models/select-option.model';
import { GameGroupFiltersService } from '../../../../../common/services/game-group-filters.service';
import { GameGroupService } from '../../../../../common/services/game-group.service';
import { GameService } from '../../../../../common/services/game.service';
import { Currency, GameInfo, isGameSupportGameGroupFilters } from '../../../../../common/typings';
import { GameGroupFiltersDeleteDialogComponent } from './game-group-filters-delete-dialog/game-group-filters-delete-dialog.component';
import { GameGroupFiltersDialogComponent } from './game-group-filters-dialog/game-group-filters-dialog.component';
import { SCHEMA_LIST } from './schema';

@Component({
    selector: 'game-group-filters',
    templateUrl: './game-group-filters.component.html',
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: GameGroupFiltersService },
    ],
    standalone: false
})
export class GameGroupFiltersComponent implements OnDestroy {

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<GameGroupFilter>;

  @Input()
  set entity( value: Entity ) {
    if (!value) {
      return;
    }

    this._entity = value;
    this.useEntityPathInGridRequests();
  }

  get entity(): Entity {
    return this._entity;
  }

  @Input() public currencies: Currency[];

  schema = SCHEMA_LIST;

  gameGroupsSelectOptions: SelectOptionModel[];

  games: GameSelectItem[];
  filteredGames: GameInfo[];
  gameGroups: GameGroup[];

  specificGames: GameInfo[] = [];

  public rowActions: RowAction[] = [];

  private _entity: Entity;
  private destroyed$ = new Subject<void>();

  constructor( private dialog: MatDialog,
               private gameService: GameService,
               private gameGroupService: GameGroupService,
  ) {
    this.setRowActions();
  }

  ngOnInit() {
    this.initGameSelectItems();
    this.initGameGroups();
  }

  setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.editTooltip',
        icon: 'edit',
        inMenu: false,
        fn: ( gameGroupFilter: GameGroupFilter ) => this.showGameGroupFiltersModal(gameGroupFilter),
        canActivateFn: ( gameGroupFilter: GameGroupFilter ) => gameGroupFilter.group?.isOwner,
      }),
      new RowAction({
        title: 'ENTITY_SETUP.GAME_GROUP_FILTERS.GRID.deleteTooltip',
        icon: 'delete',
        inMenu: false,
        fn: ( gameGroupFilter: GameGroupFilter ) => this.openRemoveDialog(gameGroupFilter),
        canActivateFn: ( gameGroupFilter: GameGroupFilter ) => gameGroupFilter.group?.isOwner,
      })
    ];
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initGameSelectItems() {
    this.gameService.getAllGames(this.entity.path, true, true)
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(
        ( games: GameInfo[] ) => {
          this.filteredGames = games.filter(
            game => {
              if (isGameSupportGameGroupFilters(game)) {
                return true;
              } else {
                this.specificGames.push(game);

                return false;
              }
            }
          );

          this.games = this.filteredGames?.map(( game: GameInfo ) => fromGameInfo(game));
          if (Array.isArray(this.specificGames) && this.specificGames.length) {
            this.specificGames = this.specificGames.filter(( specificGame: GameInfo ) => specificGame.type === 'slot');
          }
        }
      );
  }

  initGameGroups() {
    this.gameGroupService.getGameGroupsList(this.entity.path)
      .pipe(
        takeUntil(this.destroyed$),
      ).subscribe(gameGroups => {
      this.gameGroups = gameGroups;

      const groupId = this.schema.find(item => item.field === 'groupId') as SelectInputOptionData;

      groupId.data = gameGroups;
    });
  }

  onClick( { row }: { field: string, row: GameGroupFilter } ) {
    this.showGameGroupFiltersModal(row);
  }

  showGameGroupFiltersModal( gameGroupFilter?: GameGroupFilter ) {
    const dialogRef = this.dialog.open(GameGroupFiltersDialogComponent, {
      width: '1100px',
      disableClose: true,
      data: {
        gameGroupFilter: gameGroupFilter || undefined,
        entity: this.entity,
        currencies: this.currencies,
        games: this.games,
        filteredGames: this.filteredGames,
        specificGames: this.specificGames,
      }
    });

    dialogRef.afterClosed().subscribe(
      () => this.grid.dataSource.loadData()
    );
  }

  openRemoveDialog( gameGroupFilter: GameGroupFilter ) {
    let gameGroup = this.gameGroups?.find(item => item.id === gameGroupFilter.groupId)?.name;

    const dialogRef = this.dialog.open(GameGroupFiltersDeleteDialogComponent, {
      width: '500px',
      data: {
        path: this.entity.path,
        gameGroup: gameGroup,
        filterId: gameGroupFilter.id,
      },
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(
      () => this.grid.dataSource.loadData()
    );
  }

  private useEntityPathInGridRequests() {
    this.grid.dataSource.requestData = {
      path: this.entity.path
    };
  }
}
