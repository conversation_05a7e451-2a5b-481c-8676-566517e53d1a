import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Entity } from '../../../../../common/models/entity.model';
import { Currency } from '../../../../../common/typings';
import { SetupEntityService } from '../setup-entity.service';

@Component({
    selector: 'tab-game-group-filter',
    templateUrl: './tab-game-group-filters.component.html',
    standalone: false
})
export class TabGameGroupFiltersComponent {
  readonly entity?: Entity;
  readonly currencies?: Currency[];

  constructor( { entity }: SetupEntityService,
               { snapshot: { data: { currencies } } }: ActivatedRoute,
  ) {
    this.entity = entity;
    this.currencies = currencies;
  }
}
