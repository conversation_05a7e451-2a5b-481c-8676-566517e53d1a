import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import {
  SwuiChipsAutocompleteModule, SwuiControlMessagesModule, SwuiGamesSelectManagerModule, SwuiGridModule, SwuiSelectModule
} from '@skywind-group/lib-swui';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { HintsModule } from '../../../../../common/components/hints/hints.module';
import { BaIfAllowedModule } from '../../../../../common/directives/baIfAllowed/baIfAllowed.module';
import { PipesModule } from '../../../../../common/pipes/pipes.module';
import { GameGroupFiltersService } from '../../../../../common/services/game-group-filters.service';
import { GameGroupService } from '../../../../../common/services/game-group.service';
import { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';
import { GameGroupFiltersDeleteDialogComponent } from './game-group-filters-delete-dialog/game-group-filters-delete-dialog.component';
import { GameGroupFiltersDialogComponent } from './game-group-filters-dialog/game-group-filters-dialog.component';
import { GameGroupFiltersComponent } from './game-group-filters.component';
import { TabGameGroupFiltersComponent } from './tab-game-group-filters.component';
import { TabGameGroupFiltersRoutingModule } from './tab-game-group-filters.routing';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ReactiveFormsModule,
    FormsModule,
    TabGameGroupFiltersRoutingModule,
    ControlMessagesModule,
    BaIfAllowedModule,
    PipesModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatTooltipModule,
    SwuiSelectModule,
    SwuiControlMessagesModule,
    SwuiChipsAutocompleteModule,
    SwuiGamesSelectManagerModule,
    SwuiGridModule,
    MatDialogModule,
    MatInputModule,
    MatCheckboxModule,
    LayoutModule,
    MatRadioModule,
    HintsModule,
  ],
  declarations: [
    TabGameGroupFiltersComponent,
    GameGroupFiltersComponent,
    GameGroupFiltersDialogComponent,
    GameGroupFiltersDeleteDialogComponent
  ],
  providers: [
    GameGroupService,
    GameGroupFiltersService,
    CurrenciesResolver,
  ],
})
export class TabGameGroupFiltersModule {
}
