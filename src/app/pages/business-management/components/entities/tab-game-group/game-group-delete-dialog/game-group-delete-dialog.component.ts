import { Component, Inject } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { GameGroup } from '../../../../../../common/models/game-group.model';


@Component({
    selector: 'game-group-delete-dialog',
    templateUrl: 'game-group-delete-dialog.component.html',
    styleUrls: ['game-group-delete-dialog.component.scss'],
    standalone: false
})
export class GameGroupDeleteDialogComponent {
  gameGroup: GameGroup;
  isForceControl: FormControl = new FormControl(false);

  constructor( private dialogRef: MatDialogRef<GameGroupDeleteDialogComponent>,
               @Inject(MAT_DIALOG_DATA) public data: { gameGroup: GameGroup } ) {
    this.gameGroup = data.gameGroup;
  }

  cancel() {
    this.dialogRef.close();
  }

  submit() {
    this.dialogRef.close({ isForceDelete: this.isForceControl.value });
  }
}
