import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  RowAction, SwHubAuthService, SwuiGridComponent, SwuiGridDataService, SwuiNotificationsService, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { iif, Subject, throwError } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';

import { PERMISSIONS_LIST } from '../../../../../app.constants';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { GameGroup } from '../../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../../common/models/select-option.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { GameGroupService } from '../../../../../common/services/game-group.service';
import { GameGroupDeleteDialogComponent } from './game-group-delete-dialog/game-group-delete-dialog.component';
import { GameGroupDialogComponent } from './game-group-dialog/game-group-dialog.component';
import { SCHEMA_LIST } from './schema';


@Component({
    selector: 'game-group',
    templateUrl: './game-group.component.html',
    styleUrls: ['./game-group.component.scss'],
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: GameGroupService },
    ],
    standalone: false
})
export class GameGroupComponent implements OnInit, OnDestroy {

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<GameGroup>;

  @Input()
  set entity( value: Entity ) {
    if (!value) {
      return;
    }

    this._entity = value;
    this.useEntityPathInGridRequests();
  }

  get entity(): Entity {
    return this._entity;
  }

  schema = SCHEMA_LIST;

  gameGroups: GameGroup[] = [];
  defaultGroupControl: FormControl = new FormControl();

  gameGroupsSelectOptions: SelectOptionModel[];
  entitySettingsPermissionsList = PERMISSIONS_LIST.ENTITY_SETTINGS;
  createPermissionsList = PERMISSIONS_LIST.GAME_GROUP_CREATE;
  editPermissionsList = PERMISSIONS_LIST.GAME_GROUP_EDIT;
  deletePermissionsList = PERMISSIONS_LIST.GAME_GROUP_DELETE;

  public rowActions: RowAction[] = [];

  private _entity: Entity;
  private destroyed$ = new Subject<void>();

  constructor( private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
               private translate: TranslateService,
               private notifications: SwuiNotificationsService,
               private gameGroupService: GameGroupService,
               private dialog: MatDialog,
               private authService: SwHubAuthService,
  ) {
    this.setRowActions();
  }

  ngOnInit() {
    this.entitySettingsService.getSettings(this.entity.path)
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( data: EntitySettingsModel ) => {
          if (data && data.defaultGameGroup) {
            this.defaultGroupControl.setValue(data.defaultGameGroup,
              { emitEvent: false }
            );
          }
        }
      );

    this.gameGroupService.getGameGroupsList(this.entity.path)
      .pipe(
        takeUntil(this.destroyed$)
      )
      .subscribe(( data: GameGroup[] ) => {
          this.gameGroups = data;
          this.buildGameGroupsSelectOptions();
        }
      );
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.GAME_GROUP.editGameGroupTooltip',
        icon: 'edit',
        inMenu: false,
        fn: ( gameGroup: GameGroup ) => this.showGameGroupModal(gameGroup),
        canActivateFn: ( gameGroup: GameGroup ) => this.authService.allowedTo(this.editPermissionsList) && gameGroup?.isOwner,
      }),
      new RowAction({
        title: 'ENTITY_SETUP.GAME_GROUP.removeGameGroupTooltip',
        icon: 'delete',
        inMenu: false,
        fn: ( gameGroup: GameGroup ) => this.openRemoveDialog(gameGroup),
        canActivateFn: ( gameGroup: GameGroup ) => this.authService.allowedTo(this.deletePermissionsList) && gameGroup?.isOwner,
      })
    ];
  }

  onClick( { row }: { field: string, row: GameGroup } ) {
    this.showGameGroupModal(row);
  }

  showGameGroupModal( gameGroup?: GameGroup ) {
    const dialogRef = this.dialog.open(GameGroupDialogComponent, {
      width: '500px',
      data: {
        gameGroup: gameGroup || undefined,
      },
      disableClose: true
    });

    dialogRef.afterClosed()
      .pipe(
        filter(data => !!data),
        switchMap(( data: GameGroup ) => {
          const path = this.entity ? this.entity.path : null;
          return iif(() => !!gameGroup,
            this.gameGroupService.editGameGroup(data, path, gameGroup?.name),
            this.gameGroupService.createGameGroup(data, path));
        }),
        switchMap(( val: GameGroup ) => {
          const message = !!gameGroup ? 'ENTITY_SETUP.GAME_GROUP.notificationEdited' : 'ENTITY_SETUP.GAME_GROUP.notificationCreated';
          return this.translate.get(message, { gameGroup: val.name });
        }),
        tap(( message: string ) => this.notifications.success(message)),
        switchMap(() => this.gameGroupService.getGameGroupsList(this.entity.path)),
        takeUntil(this.destroyed$)
      )
      .subscribe(( val: GameGroup[] ) => {
        this.grid.dataSource.loadData();
        this.updateGroupsList(val);
      });
  }

  openRemoveDialog( gameGroup: GameGroup ) {
    const dialogRef = this.dialog.open(GameGroupDeleteDialogComponent, {
      width: '500px',
      data: { gameGroup },
      disableClose: true
    });

    dialogRef.afterClosed()
      .pipe(
        filter(val => !!val),
        switchMap(( val: { isForceDelete: boolean } ) => {
          return this.gameGroupService.removeGameGroup(this.entity.path, gameGroup.name, val.isForceDelete);
        }),
        switchMap(() => this.translate.get('ENTITY_SETUP.GAME_GROUP.gameGroupRemoved', { gameGroup: gameGroup.name })),
        tap(( message: string ) => this.notifications.success(message)),
        switchMap(() => this.gameGroupService.getGameGroupsList(this.entity.path)),
        takeUntil(this.destroyed$)
      )
      .subscribe(
        ( val: GameGroup[] ) => {
          if (this.defaultGroupControl.value === gameGroup) {
            this.defaultGroupControl.setValue(null);
          }

          this.grid.dataSource.loadData();
          this.updateGroupsList(val);
        },
        (( error: HttpErrorResponse ) => {
          if (error.error?.code === 236) {
            let removableGroup = JSON.parse(JSON.stringify(gameGroup));
            removableGroup.isDefault = true;

            this.openRemoveDialog(removableGroup);
          } else {
            return throwError(error);
          }
        }));
  }

  submit( event: Event ) {
    event.preventDefault();
    this.entitySettingsService.patchSettings({ defaultGameGroup: this.defaultGroupControl.value || null }, this.entity.path)
      .pipe(
        switchMap(( val: EntitySettingsModel ) => {
          return this.translate.get('ENTITY_SETUP.GAME_GROUP.notificationDefaultGameGroupChanged', { gameGroup: val.defaultGameGroup });
        }),
        tap(( message: string ) => this.notifications.success(message)),
        switchMap(() => this.gameGroupService.getGameGroupsList(this.entity.path)),
        takeUntil(this.destroyed$)
      )
      .subscribe(( val: GameGroup[] ) => {
        this.grid.dataSource.loadData();
        this.updateGroupsList(val);
      });
  }

  private buildGameGroupsSelectOptions() {
    this.gameGroupsSelectOptions = [];

    if (this.gameGroups) {
      this.gameGroups.map(( gameGroup: any ) => this.gameGroupsSelectOptions.push(
        new GameGroup(gameGroup).toSelectOption())
      );
    }
  }

  private updateGroupsList( val: GameGroup[] ) {
    this.gameGroups = val;
    this.buildGameGroupsSelectOptions();

    if (this.gameGroups) {
      const defaultGameGroup = this.gameGroups.find(item => item.isDefault);
      if (defaultGameGroup) {
        this.defaultGroupControl.setValue(defaultGameGroup.name, { emitEvent: false });
      }
    }
  }

  private useEntityPathInGridRequests() {
    this.grid.dataSource.requestData = {
      path: this.entity.path
    };
  }
}
