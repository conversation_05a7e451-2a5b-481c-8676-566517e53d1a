import { Component } from '@angular/core';
import { Entity } from '../../../../../common/models/entity.model';
import { SetupEntityService } from '../setup-entity.service';

@Component({
    selector: 'tab-game-group',
    templateUrl: 'tab-game-group.component.html',
    standalone: false
})

export class TabGameGroupComponent {
  readonly entity?: Entity;

  constructor( { entity }: SetupEntityService ) {
    this.entity = entity;
  }
}
