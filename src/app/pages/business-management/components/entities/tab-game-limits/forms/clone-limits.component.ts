import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { SelectOptionModel } from '../../../../../../common/models/select-option.model';
import { GameGroupService } from '../../../../../../common/services/game-group.service';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { Game } from '../../../../../../common/typings';
import { MESSAGE_ERROR } from '../tab-game-limits.constants';


export interface CloneLimitsDialogData {
  entityPath: string;
  games: Game[];
  gameGroupsSelectOptions: SelectOptionModel[];
  gamesSelectOptions: SelectOptionModel[];
  limits: any;
  selectedGameCodeFrom: string;
  selectedGameGroupFrom: string;
}

@Component({
    selector: 'clone-limits',
    templateUrl: './clone-limits.component.html',
    styleUrls: ['../game-limits.component.scss'],
    standalone: false
})
export class CloneLimitsComponent implements OnInit {
  public messageErrors: ErrorMessage = MESSAGE_ERROR;
  public gameGroupsSelectOptions: SelectOptionModel[] = [];
  public gamesSelectOptions: SelectOptionModel[] = [];
  public games: Game[] = [];

  public entityPath: string = '';
  public selectedGameGroupFrom: string = '';
  public selectedGameCodeFrom: string = '';
  public limits: any;

  public cloneForm: FormGroup;

  public isLimitsAlreadyExist: boolean = false;
  public submitted: boolean = false;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private gameGroupsService: GameGroupService,
    public dialogRef: MatDialogRef<CloneLimitsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CloneLimitsDialogData
  ) {
    this.setInitialState(data);
  }

  ngOnInit() {
    this.initForm();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  onConfirmClick() {
    this.clone();
  }

  get gameGroupToControl(): FormControl {
    return this.cloneForm.get('gameGroupTo') as FormControl;
  }

  get gameToControl(): FormControl {
    return this.cloneForm.get('gameTo') as FormControl;
  }

  initForm() {
    this.cloneForm = this.fb.group({
      gameGroupTo: ['', Validators.required],
      gameTo: ['', Validators.required],
    }, {
      validator: ValidationService.gamesAndGameGroupsAreNotTheSame('gameGroupTo', 'gameTo',
        this.selectedGameGroupFrom, this.selectedGameCodeFrom),
    });

    this.cloneForm.valueChanges.pipe(
      tap(() => this.isLimitsAlreadyExist = false),
      filter(val => !!val.gameGroupTo && !!val.gameTo),
      switchMap(params => this.gameGroupsService.getGameLimits(this.entityPath, params.gameGroupTo, params.gameTo)),
      takeUntil(this.destroyed$)
    ).subscribe(( limits ) => this.isLimitsAlreadyExist = limits && this.cloneForm.valid);
  }

  getGameName( gameCode ): string {
    return this.games.find(game => game.code === gameCode).title;
  }

  clone() {
    const { gameGroupTo, gameTo } = this.cloneForm.value;

    this.dialogRef.close({
      limits: this.limits,
      entityPath: this.entityPath,
      gameGroup: gameGroupTo,
      game: gameTo,
      isLimitsAlreadyExist: this.isLimitsAlreadyExist
    });
  }

  private setInitialState( initialState: CloneLimitsDialogData ) {
    this.entityPath = initialState.entityPath;
    this.gameGroupsSelectOptions = initialState && initialState.gameGroupsSelectOptions ? initialState.gameGroupsSelectOptions : [];
    this.games = initialState && initialState.games ? initialState.games : [];
    this.gamesSelectOptions = initialState && initialState.gamesSelectOptions ? initialState.gamesSelectOptions : [];
    this.limits = initialState && initialState.limits ? initialState.limits : [];
    this.selectedGameCodeFrom = initialState.selectedGameCodeFrom;
    this.selectedGameGroupFrom = initialState.selectedGameGroupFrom;
  }
}
