import { Component, Inject, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { GameGroup } from '../../../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../../../common/models/select-option.model';
import { GameGroupService } from '../../../../../../common/services/game-group.service';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { Game } from '../../../../../../common/typings';
import { StakeAllRange } from '../../../../../../common/typings/stake-all-range';
import { MESSAGE_ERROR } from '../tab-game-limits.constants';

export interface LimitsDialogData {
  currencySelectOptions: SelectOptionModel[];
  gamesSelectOptions: SelectOptionModel[];
  games: Game[];
  entityPath: string;
  entityName: string;
  selectedGameGroup: string;
  selectedGameCode: string;
  totalBetMultiplier: number;
  allowOverrideStakeAll: boolean;
  stakeAllRanges: StakeAllRange[] | undefined;
  isEdit?: boolean;
}

@Component({
    selector: 'create-limits',
    templateUrl: './create-limits.component.html',
    styleUrls: ['../game-limits.component.scss'],
    standalone: false
})
export class CreateLimitsComponent implements OnInit {
  public messageErrors: ErrorMessage = MESSAGE_ERROR;
  public currencySelectOptions: SelectOptionModel[] = [];
  public gamesSelectOptions: SelectOptionModel[] = [];
  public games: Game[] = [];

  public totalBetMultiplier: number;
  public allowOverrideStakeAll: boolean;
  public stakeAllRanges: StakeAllRange[] | undefined;

  public form: FormGroup;
  public limitsForm: FormGroup;
  public items: FormArray;

  public isGameGroupContainGame: boolean = false;
  public submitted: boolean = false;
  public isEdit: boolean = false;
  public isReadMode: boolean = false;

  public selectedGameGroup: string = '';
  public selectedGameCode: string = '';
  public entityPath: string = '';
  public entityName: string = '';
  public modalTitle: string = '';

  public gameGroupsSelectOptions: SelectOptionModel[] = [];

  private readonly destroyed$ = new Subject<void>();

  constructor( private fb: FormBuilder,
               private notifications: SwuiNotificationsService,
               private gameGroupsService: GameGroupService,
               public dialogRef: MatDialogRef<CreateLimitsComponent>,
               @Inject(MAT_DIALOG_DATA) public data: LimitsDialogData
  ) {
    this.setInitialState(data);
    this.modalTitle = this.isEdit ?
      'ENTITY_SETUP.GAME_LIMITS.editLimitsTitle' :
      'ENTITY_SETUP.GAME_LIMITS.createLimitsTitle';
  }

  ngOnInit() {
    this.initForm();
    this.initLimitsForm();
    this.buildGameGroupsSelectOptions();
    this.enableCurrencyOptions();

    if (this.isEdit) {
      this.gameLabelControl.disable();
      this.gameGroupControl.disable();

      if (this.isReadMode) {
        this.form.disable();
      }
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  onConfirmClick() {
    this.submit();
  }

  get gameGroupControl(): FormControl {
    return this.form.get('gameGroup') as FormControl;
  }

  get gameLabelControl(): FormControl {
    return this.form.get('game') as FormControl;
  }

  get currencyControl(): FormControl {
    return this.form.get('currency') as FormControl;
  }

  public initForm() {
    this.form = this.fb.group({
      gameGroup: ['', Validators.required],
      game: ['', Validators.required],
      currency: ['']
    });

    if (this.selectedGameGroup && this.selectedGameCode) {
      this.gameGroupControl.setValue(this.selectedGameGroup);
      this.gameLabelControl.setValue(this.selectedGameCode);
      this.checkIfGameGroupContainsGame();
    }

    this.gameGroupControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      ).subscribe(gameGroup => {
        this.enableCurrencyOptions();
        if (gameGroup) {
          this.selectedGameGroup = gameGroup;
          this.checkIfGameGroupContainsGame();
        } else {
          this.initLimitsForm();
          this.selectedGameGroup = null;
          this.gameLabelControl.patchValue(null);
          this.currencyControl.patchValue(null);
        }
      }
    );

    this.gameLabelControl.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      ).subscribe(gameCode => {
        this.enableCurrencyOptions();
        if (gameCode) {
          this.selectedGameCode = gameCode;
          let selectedGame = this.games.find(game => game.code === gameCode);
          this.totalBetMultiplier = +selectedGame?.totalBetMultiplier;

          this.checkIfGameGroupContainsGame();
        } else {
          this.initLimitsForm();
          this.selectedGameCode = null;
          this.currencyControl.patchValue(null);
        }
      }
    );
  }

  public enableCurrencyOptions() {
    this.currencySelectOptions = this.currencySelectOptions.map(
      item => {
        item.disabled = false;

        return item;
      }
    );
  }

  public initLimitsForm() {
    this.limitsForm = this.fb.group({
      items: this.fb.array([]),
    });
  }

  public createItem( currency ): FormGroup {
    let range = this.getCurrencyStakeAllRange(currency);

    const stakeAllValidators = [
      Validators.required,
      ValidationService.stakeAllValidator(),
      ValidationService.arrayDuplicatesValidator(),
    ];

    if (!this.allowOverrideStakeAll) {
      stakeAllValidators.push(ValidationService.validateStakeAllValues(range));
    }

    const newGroup = this.fb.group({
      currency,
      stakeAll: [
        { value: '', disabled: this.isReadMode },
        Validators.compose(stakeAllValidators)
      ],
      stakeMin: [''],
      stakeMax: [''],
      stakeDef: [
        { value: '', disabled: this.isReadMode },
        Validators.compose([
          Validators.required,
          ValidationService.validateStakeDef()
        ])
      ],
      minTotalStake: [''],
      maxTotalStake: [''],
    });

    const stakeAll = newGroup.get('stakeAll');
    const stakeDef = newGroup.get('stakeDef');

    stakeAll.valueChanges.pipe(
      distinctUntilChanged(),
      takeUntil(this.destroyed$)
    ).subscribe(() => stakeDef.updateValueAndValidity({ onlySelf: true }));
    stakeAll.markAsTouched();
    stakeDef.markAsTouched();

    return newGroup;
  }

  public getCurrencyStakeAllRange( currency ): number[] {
    let coinBets: number[] = [];
    let obj: StakeAllRange;

    if (Array.isArray(this.stakeAllRanges) && this.stakeAllRanges.length) {
      obj = this.stakeAllRanges.find(item => item.currency === currency);
    }

    if (obj) {
      coinBets = obj.coinBets;
    }

    return coinBets;
  }

  public addItem( currency ) {
    this.items = this.limitsForm.get('items') as FormArray;

    const itemToAdd = this.createItem(currency);
    this.items.push(itemToAdd);

    itemToAdd.valueChanges
      .pipe(
        takeUntil(this.destroyed$)
      ).subscribe(
      data => {
        if (itemToAdd.get('stakeAll').valid) {
          let stakeAll = data.stakeAll.split(',').map(Number);

          let stakeMin = Math.min(...stakeAll);
          let stakeMax = Math.max(...stakeAll);

          let minTotalStake = Number((stakeMin * this.totalBetMultiplier).toFixed(2));
          let maxTotalStake = Number((stakeMax * this.totalBetMultiplier).toFixed(2));

          itemToAdd.get('stakeMin').setValue(stakeMin, { emitEvent: false });
          itemToAdd.get('stakeMax').setValue(stakeMax, { emitEvent: false });

          itemToAdd.get('minTotalStake').setValue(minTotalStake, { emitEvent: false });
          itemToAdd.get('maxTotalStake').setValue(maxTotalStake, { emitEvent: false });
        } else {
          itemToAdd.get('stakeMin').setValue(null, { emitEvent: false });
          itemToAdd.get('stakeMax').setValue(null, { emitEvent: false });
          itemToAdd.get('minTotalStake').setValue(null, { emitEvent: false });
          itemToAdd.get('maxTotalStake').setValue(null, { emitEvent: false });
        }
      }
    );
  }

  public removeItem( currency ) {
    this.currencySelectOptions = this.currencySelectOptions.map(item => {
      if (item.id === currency) {
        item.disabled = false;
      }
      return item;
    });

    this.limitsFormArray.removeAt(this.limitsFormArray.value.findIndex(item => {
      return item.currency === currency;
    }));
  }

  public addCurrency( currency: string ) {
    this.currencyControl.patchValue(null);

    this.currencySelectOptions = this.currencySelectOptions.map(item => {
      if (item.id === currency) {
        item.disabled = true;
      }
      return item;
    });

    this.addItem(currency);
  }

  public get limitsFormArray() {
    return this.limitsForm.get('items') as FormArray;
  }

  buildGameGroupsSelectOptions() {
    this.gameGroupsService.getGameGroupsList(this.entityPath)
      .pipe(
        takeUntil(this.destroyed$)
      ).subscribe(
      data => {
        this.gameGroupsSelectOptions = data?.filter(( gameGroup: GameGroup ) => gameGroup.isOwner)?.map(
          ( gameGroup: GameGroup ) => new GameGroup(gameGroup).toSelectOption()
        );
      }
    );
  }

  checkIfGameGroupContainsGame() {
    this.isGameGroupContainGame = false;

    if (this.selectedGameGroup && this.selectedGameCode) {

      this.gameGroupsService.getGameLimits(this.entityPath, this.selectedGameGroup, this.selectedGameCode)
        .pipe(
          takeUntil(this.destroyed$)
        ).subscribe(
        limits => {
          this.initLimitsForm();
          if (limits) {

            let response = JSON.parse(JSON.stringify(limits));
            let modifyResponse = { items: [] };

            Object.entries(response).forEach(( [key, value] ) => {
              value['currency'] = key;
              value['stakeAll'] = value['stakeAll'].join(', ');
              modifyResponse.items.push(value);
              this.addCurrency(key);
            });

            this.isGameGroupContainGame = true;

            this.limitsForm.patchValue(modifyResponse as any);
          }
        }
      );
    }
  }

  setCoinBets( item: AbstractControl ) {
    const currencyCode = item.get('currency').value;
    const stakeAllRange = this.getCurrencyStakeAllRange(currencyCode).toString();
    if (!stakeAllRange.length) {
      this.notifications.error(`The list of coins for the currency: ${currencyCode} is not specified. You can set any coins.`);
    }
    item.get('stakeAll').setValue(stakeAllRange);
  }

  submit() {
    if (this.form.valid && this.limitsForm.valid) {
      let gameGroup = this.gameGroupControl.value;
      let gameCode = this.gameLabelControl.value;

      let limitFormValue = JSON.parse(JSON.stringify(this.limitsForm.value));

      const modifyFormValue = ( array, keyField ) =>
        array.reduce(( obj, item ) => {
          obj[item[keyField]] = item;
          return obj;
        }, {});

      const body = modifyFormValue(limitFormValue.items, 'currency');
      Object.entries(body).forEach(( [, value] ) => {
        delete value['currency'];
        delete value['minTotalStake'];
        value['stakeAll'] = value['stakeAll'].split(',').map(Number);
        value['stakeDef'] = parseFloat(value['stakeDef']);
      });

      this.dialogRef.close({
        body: body,
        entityPath: this.entityPath,
        gameGroup: gameGroup,
        gameCode: gameCode,
        isGameGroupContainGame: this.isGameGroupContainGame
      });
    }
  }

  private setInitialState( initialState: LimitsDialogData ) {
    if (initialState) {
      this.currencySelectOptions = initialState && initialState.currencySelectOptions ? initialState.currencySelectOptions : [];
      this.gamesSelectOptions = initialState && initialState.gamesSelectOptions ? initialState.gamesSelectOptions : [];
      this.games = initialState && initialState.games ? initialState.games : [];
      this.entityPath = initialState.entityPath;
      this.entityName = initialState.entityName;
      this.selectedGameGroup = initialState.selectedGameGroup;
      this.selectedGameCode = initialState.selectedGameCode;
      this.totalBetMultiplier = initialState.totalBetMultiplier;
      this.allowOverrideStakeAll = initialState.allowOverrideStakeAll;
      this.stakeAllRanges = initialState.stakeAllRanges;

      this.isEdit = initialState.isEdit;

      let selectedGame = this.games?.find(( game: Game ) => game.code === this.selectedGameCode);
      if (selectedGame) {
        this.isReadMode = !selectedGame.features?.isCustomLimitsSupported;
      }
    }
  }
}
