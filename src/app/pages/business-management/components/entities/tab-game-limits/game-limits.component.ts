import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  RowAction, SwHubAuthService, SwuiGridComponent, SwuiGridDataService, SwuiNotificationsService, SwuiSelectOption, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { of, Subject } from 'rxjs';
import { catchError, filter, finalize, switchMap, take, takeUntil, tap } from 'rxjs/operators';

import { BoConfirmationComponent } from '../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { CurrencyModel } from '../../../../../common/models/currency.model';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { GameGroup } from '../../../../../common/models/game-group.model';
import { SelectOptionModel } from '../../../../../common/models/select-option.model';
import { CurrencyService } from '../../../../../common/services/currency.service';
import { GameGroupService } from '../../../../../common/services/game-group.service';
import { GameLimitsService } from '../../../../../common/services/game-limits.service';
import { GameService } from '../../../../../common/services/game.service';
import { StakeRangesService } from '../../../../../common/services/stake-ranges.service';
import { Currency, Game } from '../../../../../common/typings';
import { GameLimits } from '../../../../../common/typings/game-limits';
import { StakeAllRange } from '../../../../../common/typings/stake-all-range';
import { CloneLimitsComponent, CloneLimitsDialogData } from './forms/clone-limits.component';
import { CreateLimitsComponent, LimitsDialogData } from './forms/create-limits.component';
import { SCHEMA_FILTER, SCHEMA_LIST } from './limits.schema';

interface GameLimitsInfo {
  game: Game;
  gamegroup: GameGroup;
  limits: any[];
  overrideDefault: boolean;
}

@Component({
    selector: 'game-limits',
    templateUrl: './game-limits.component.html',
    styleUrls: ['./game-limits.component.scss'],
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: GameLimitsService },
    ],
    standalone: false
})
export class GameLimitsComponent implements OnInit {
  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<GameLimits>;

  @Input() public entitySettings: EntitySettingsModel;

  @Input()
  set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
    this.useEntityPathInGridRequests();
  }

  get entity(): Entity {
    return this._entity;
  }

  public allowOverrideStakeAll: boolean;
  public stakeAllRanges: StakeAllRange[] | undefined;
  public gameGroupsSelectOptions: SelectOptionModel[] = [];
  public gameGroups: GameGroup[] = [];
  public gamesSelectOptions: SelectOptionModel[] = [];
  public currencySelectOptions: SelectOptionModel[] = [];
  public rowActions: RowAction[] = [];
  public games: Game[] = [];
  public schema = SCHEMA_LIST;
  public filterSchema = SCHEMA_FILTER;
  public readOnly = false;

  private _entity: Entity;
  private readonly destroyed$ = new Subject<void>();

  constructor( private gameGroupsService: GameGroupService,
               private gameService: GameService,
               private currencyService: CurrencyService<Currency>,
               private notifications: SwuiNotificationsService,
               private translation: TranslateService,
               private dialog: MatDialog,
               private stakeRangesService: StakeRangesService,
               private authService: SwHubAuthService,
  ) {
  }

  ngOnInit() {
    this.initStakeAllRanges();
    this.setRowActions();
    this.initGameGroups();
    this.buildGamesSelectOptions();
    this.buildCurrencySelectOptions();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initStakeAllRanges() {
    this.allowOverrideStakeAll = this.entitySettings.allowOverrideStakeAll;

    if (!this.allowOverrideStakeAll) {
      if (this.authService.allowedTo(['stake-range'])) {
        this.stakeRangesService.getStakeAllRanges()
          .pipe(
            take(1),
          )
          .subscribe(
            data => {
              this.stakeAllRanges = data;
            }
          );
      } else {
        this.readOnly = true;
      }
    }
  }

  initGameGroups() {
    this.gameGroupsService.getGameGroupsList(this.entity.path)
      .pipe(
        takeUntil(this.destroyed$)
      ).subscribe(
      data => {
        this.gameGroups = data;
        this.buildGameGroupsSelectOptions();
      }
    );
  }

  buildGameGroupsSelectOptions() {
    this.gameGroupsSelectOptions = [];

    if (this.gameGroups) {
      this.gameGroups.map(( gameGroup: any ) => this.gameGroupsSelectOptions.push(
        new GameGroup(gameGroup).toSelectOption())
      );
    }

    this.filterSchema = this.filterSchema.map(( item ) => {
      if (item.field === 'gameGroup' && item.type === 'select') {
        item.data = this.gameGroupsSelectOptions;
      }

      return item;
    });
  }

  buildGamesSelectOptions() {
    this.gameService.getAllGames(this.entity.path, false, true).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( games: Game[] ) => {
      this.games = games;
      let filteredGames = games.filter(( { features, totalBetMultiplier } ) => totalBetMultiplier && features?.isCustomLimitsSupported);
      this.gamesSelectOptions = [];
      filteredGames.map(game => this.gamesSelectOptions.push(new SelectOptionModel(game.code, game.title)));

      this.filterSchema = this.filterSchema.map(item => {
        if (item.field === 'gameCode' && item.type === 'select') {
          item.data = filteredGames.map<SwuiSelectOption>(( { code, title } ) => ({ id: code, text: `${title} (${code})` }));
        }
        return item;
      });
    });
  }

  buildCurrencySelectOptions() {
    this.currencyService.getList()
      .pipe(
        takeUntil(this.destroyed$)
      ).subscribe(
      currencies => {
        this.currencySelectOptions = [];
        currencies.map(( currency: any ) => this.currencySelectOptions.push(
          (new CurrencyModel(currency)).toSelectOption()));
      }
    );
  }

  splitStakeAll( stakeAll: number[] ): string {
    return stakeAll.join(', ');
  }

  showLimitsModal( selectedGameGroup?, game? ) {
    let initialState = selectedGameGroup && game ? {
      gamesSelectOptions: this.gamesSelectOptions,
      currencySelectOptions: this.currencySelectOptions,
      games: this.games,
      entityPath: this.entity.path,
      entityName: this.entity.name,
      selectedGameGroup,
      selectedGameCode: game.code,
      totalBetMultiplier: game.totalBetMultiplier,
      allowOverrideStakeAll: this.allowOverrideStakeAll,
      stakeAllRanges: this.stakeAllRanges,
      isEdit: true,
    } : {
      gamesSelectOptions: this.gamesSelectOptions,
      currencySelectOptions: this.currencySelectOptions,
      games: this.games,
      entityPath: this.entity.path,
      entityName: this.entity.name,
      allowOverrideStakeAll: this.allowOverrideStakeAll,
      stakeAllRanges: this.stakeAllRanges,
      isEdit: false,
    };

    this.dialog.open<LimitsDialogData>(CreateLimitsComponent, {
      width: '1000px',
      data: initialState,
      panelClass: 'dialogStyle',
      disableClose: true
    }).afterClosed().pipe(
      filter(data => !!data),
      switchMap(data => this.gameGroupsService.updateGameLimits(
        data.body, data.entityPath, data.gameGroup, data.gameCode, data.isGameGroupContainGame)),
      switchMap(() => this.translation.get(('ENTITY_SETUP.GAME_LIMITS.limitsUpdated'))),
      tap(( message: string ) => this.notifications.success(message, '')),
      catchError(( error: HttpErrorResponse ) => of(this.notifications.error(error.error.message))),
      finalize(() => this.grid.dataSource.loadData()),
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  onCloneLimitsAction( gameGroup, game, limits ) {
    this.dialog.open<CloneLimitsDialogData>(CloneLimitsComponent, {
      width: '1000px',
      data: {
        entityPath: this.entity.path,
        gameGroupsSelectOptions: this.gameGroupsSelectOptions,
        gamesSelectOptions: this.gamesSelectOptions,
        games: this.games,
        selectedGameGroupFrom: gameGroup,
        selectedGameCodeFrom: game.code,
        limits: limits
      },
      disableClose: true,
      panelClass: 'dialogStyle',
    }).afterClosed().pipe(
      filter(data => !!data),
      switchMap(data => this.gameGroupsService.updateGameLimits(
        data.limits, data.entityPath, data.gameGroup, data.game, data.isLimitsAlreadyExist)),
      switchMap(() => this.translation.get('ENTITY_SETUP.GAME_LIMITS.limitsCloned')),
      tap(( message ) => this.notifications.success(message, '')),
      catchError(( error: HttpErrorResponse ) => of(this.notifications.error(error.error.message))),
      finalize(() => this.grid.dataSource.loadData()),
      takeUntil(this.destroyed$)
    ).subscribe();
  }

  onRemoveGameAction( gameGroup, game ) {
    this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      disableClose: true,
      data: { message: 'ENTITY_SETUP.GAME_LIMITS.removeGameMessage' }
    }).afterClosed()
      .pipe(
        filter(result => !!result),
        switchMap(() => this.gameGroupsService.removeGameFromGameGroup(this.entity.path, gameGroup, game.code)),
        switchMap(() => this.translation.get('ENTITY_SETUP.GAME_LIMITS.gameRemoved')),
        tap(( message ) => this.notifications.success(message, '')),
        catchError(( error: HttpErrorResponse ) => of(this.notifications.error(error.error.message))),
        finalize(() => this.grid.dataSource.loadData()),
        takeUntil(this.destroyed$)
      ).subscribe();
  }

  private setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.GAME_LIMITS.editLimitsTooltip',
        icon: 'edit',
        inMenu: true,
        fn: ( limit: GameLimitsInfo ) => this.showLimitsModal(limit?.gamegroup?.name, limit?.game),
        canActivateFn: ( { overrideDefault } ) => !overrideDefault && !this.readOnly,
      }),
      new RowAction({
        title: 'ENTITY_SETUP.GAME_LIMITS.cloneLimitsTooltip',
        icon: 'copyright',
        inMenu: true,
        fn: ( limit: GameLimitsInfo ) => {
          this.onCloneLimitsAction(limit?.gamegroup?.name, limit?.game, limit?.limits);
        },
        canActivateFn: ( { overrideDefault } ) => !overrideDefault && !this.readOnly,
      }),
      new RowAction({
        title: 'ENTITY_SETUP.GAME_LIMITS.removeGameTooltip',
        icon: 'delete',
        inMenu: true,
        fn: ( limit: GameLimitsInfo ) => this.onRemoveGameAction(limit?.gamegroup?.name, limit?.game),
        canActivateFn: ( limit: GameLimitsInfo ) => {
          return !limit.overrideDefault && !this.readOnly && limit.game?.features?.isCustomLimitsSupported;
        },
      })
    ];
  }

  private useEntityPathInGridRequests() {
    this.grid.dataSource.requestData = {
      path: this.entity.path
    };
  }
}
