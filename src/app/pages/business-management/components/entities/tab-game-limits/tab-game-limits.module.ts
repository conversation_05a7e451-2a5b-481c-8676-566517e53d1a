import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiGridModule, SwuiSchemaTopFilterModule, SwuiSelectModule } from '@skywind-group/lib-swui';
import { BoConfirmationModule } from '../../../../../common/components/bo-confirmation/bo-confirmation.module';
import { TrimInputValueModule } from '../../../../../common/directives/trim-input-value/trim-input-value.module';
import { GameGroupService } from '../../../../../common/services/game-group.service';
import { GameLimitsService } from '../../../../../common/services/game-limits.service';
import { CurrenciesResolver } from '../../../../../common/services/resolvers/currencies.resolver';
import { StakeRangesService } from '../../../../../common/services/stake-ranges.service';
import { CloneLimitsComponent } from './forms/clone-limits.component';
import { CreateLimitsComponent } from './forms/create-limits.component';
import { GameLimitsComponent } from './game-limits.component';
import { TabGameLimitsComponent } from './tab-game-limits.component';
import { TabGameLimitsRoutingModule } from './tab-game-limits.routing';


@NgModule({
    imports: [
        BoConfirmationModule,
        CommonModule,
        MatPaginatorModule,
        ReactiveFormsModule,
        TabGameLimitsRoutingModule,
        TranslateModule.forChild(),
        MatCardModule,
        SwuiGridModule,
        MatButtonModule,
        SwuiSelectModule,
        MatFormFieldModule,
        MatDialogModule,
        LayoutModule,
        MatIconModule,
        MatExpansionModule,
        MatInputModule,
        SwuiSchemaTopFilterModule,
        SwuiControlMessagesModule,
        TrimInputValueModule,
    ],
  declarations: [
    CloneLimitsComponent,
    CreateLimitsComponent,
    GameLimitsComponent,
    TabGameLimitsComponent,
  ],
  providers: [
    CurrenciesResolver,
    GameGroupService,
    StakeRangesService,
    GameLimitsService,
  ],
})
export class TabGameLimitsModule {
}
