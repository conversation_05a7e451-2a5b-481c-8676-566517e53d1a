import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Entity } from '../../../../../../common/models/entity.model';
import { Game, isLiveGame } from '../../../../../../common/typings';

export interface GameForceRemoveDialogData {
  entity: Entity;
  games: Game[];
}

@Component({
    selector: 'game-force-remove-dialog',
    templateUrl: 'game-force-remove-dialog.component.html',
    standalone: false
})
export class GameForceRemoveDialogComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<GameForceRemoveDialogComponent, Record<string, string[]>>,
    @Inject(MAT_DIALOG_DATA) public data: GameForceRemoveDialogData,
  ) {
  }

  ngOnInit() {
  }

  public confirmRemove() {
    const { slot, live } = this.data.games.reduce(( res, curr ) => {
      if (isLiveGame(curr)) {
        res.live.push(curr.code);
      } else {
        res.slot.push(curr.code);
      }

      return res;
    }, { slot: [], live: [] });

    this.dialogRef.close({ slot, live });
  }

  public cancelRemove() {
    this.dialogRef.close();
  }
}
