import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: '',
    templateUrl: './game-settings-to-run.component.html',
    standalone: false
})
export class GameSettingsToRunComponent {
  form: FormGroup;
  languages = [];
  currencies = [];
  gameGroups = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) data: any,
    private dialogRef: MatDialogRef<GameSettingsToRunComponent>,
    fb: FormBuilder
  ) {
    const { language, currency } = data;
    this.languages = data.languages.map(( { name, code } ) => ({
      text: `${name} (${code}) ${code === language ? '[default]' : ''}`,
      id: code
    })) || [];
    this.currencies = data.currencies.map(( { displayName, code } ) => ({
      text: `${displayName} (${code}) ${code === currency ? '[default]' : ''}`,
      id: code
    })) || [];
    this.gameGroups = data.gameGroups.map(( { name, id, isDefault } ) =>
      ({ text: `${name} (${id}) ${isDefault ? '[default]' : ''}`, id: name })) || [];

    this.form = fb.group({
      currency: [null],
      language: [null],
      gameGroup: [null],
      doNotShow: [false]
    });

    if (data.state) {
      this.form.patchValue(data.state);
    }
  }

  cancel() {
    this.dialogRef.close();
  }

  confirm() {
    this.dialogRef.close(this.form.value);
  }
}
