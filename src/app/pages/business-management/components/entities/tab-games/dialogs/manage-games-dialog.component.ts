import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { forkJoin, from, Observable, of } from 'rxjs';
import { catchError, concatAll, filter, finalize, switchMap, take } from 'rxjs/operators';
import { Entity } from '../../../../../../common/models/entity.model';
import { GameGroupFilter } from '../../../../../../common/models/game-group.model';
import { GameGroupFiltersService } from '../../../../../../common/services/game-group-filters.service';
import { ERROR_GAME_DELETE_NEED_FORCE_FLAG, GameService } from '../../../../../../common/services/game.service';
import { ChangeEntityGameData, Game, GameInfo, isGameSupportGameGroupFilters, isLiveGame } from '../../../../../../common/typings';
import { GamesSetupStepperComponent } from '../games-setup-stepper/games-setup-stepper.component';
import { ManageGamesService } from '../manage-games.service';
import { ManageGamesGridComponent } from '../manage-games/manage-games-grid/manage-games-grid.component';
import { ManageGamesPreviewComponent } from '../manage-games/manage-games-preview/manage-games-preview.component';
import { ManageGamesSetupComponent } from '../manage-games/manage-games-setup/manage-games-setup.component';
import { AddSpecificGamesAlertDialogComponent } from './add-specific-games-alert-dialog.component';

export type ManageGameStepperState = 'select' | 'setup' | 'preview';

export const MANAGE_GAME_STEPPER_STATES: {
  SELECT: ManageGameStepperState, SETUP: ManageGameStepperState, PREVIEW: ManageGameStepperState
} = {
  SELECT: 'select',
  SETUP: 'setup',
  PREVIEW: 'preview'
};

export interface ManageGamesDialogData {
  entity: Entity;
  entityGames: Game[];
  allowFullManagement: boolean;
}

export interface ManageGamesDialogResult {
  addedGames?: Game[];
  removedGames?: Game[];
}

@Component({
    selector: 'manage-games-dialog',
    templateUrl: './manage-games-dialog.component.html',
    styleUrls: [
        './manage-games-dialog.component.scss',
    ],
    standalone: false
})
export class ManageGamesDialogComponent implements OnInit {

  entity: Entity;
  entityGames: Game[];
  allowFullManagement: boolean;

  disabled: boolean = false;
  addedGames: Game[];
  removedGames: Game[];

  stepperStates: { [name: string]: ManageGameStepperState } = MANAGE_GAME_STEPPER_STATES;

  @ViewChild(GamesSetupStepperComponent) private stepper: GamesSetupStepperComponent;
  @ViewChild(ManageGamesGridComponent) private gamesGrid: ManageGamesGridComponent;
  @ViewChild(ManageGamesSetupComponent) private setupGames: ManageGamesSetupComponent;
  @ViewChild(ManageGamesPreviewComponent) private previewChanges: ManageGamesPreviewComponent;

  constructor(
    public dialogRef: MatDialogRef<ManageGamesDialogComponent, ManageGamesDialogResult | undefined>,
    public service: GameService,
    private manageGamesService: ManageGamesService,
    private dialog: MatDialog,
    private gameGroupFiltersService: GameGroupFiltersService,
    @Inject(MAT_DIALOG_DATA) data: ManageGamesDialogData,
  ) {
    this.setDialogData(data);
  }

  ngOnInit() {
  }

  get applyClass(): Object {
    return {
      'disabled': this.disabled
    };
  }

  handleApplyButtonState( disabled: boolean ) {
    this.disabled = disabled;
  }

  getApplyLabel(): string {
    const translateStructurePrefix = 'DIALOG';
    let label: string = 'save';

    if (this.allowFullManagement) {
      if (this.stepper && this.stepper.selected.state === MANAGE_GAME_STEPPER_STATES.PREVIEW) {
        label = 'finish';
      }
    }

    return `${translateStructurePrefix}.${label}`;
  }

  public isPreviousStepDisabled(): boolean {
    let disabled = true;

    if (this.stepper && this.stepper.selected.state !== MANAGE_GAME_STEPPER_STATES.SELECT) {
      disabled = false;
    }

    return disabled;
  }


  onApplyFn( event: Event ) {
    event.preventDefault();

    if (this.allowFullManagement && this.stepper) {
      switch (this.stepper.selected.state as ManageGameStepperState) {
        case MANAGE_GAME_STEPPER_STATES.SELECT:
          this.gamesGrid.confirmGamesSelection(event);
          break;

        case MANAGE_GAME_STEPPER_STATES.SETUP:
          this.setupGames.completeGamesSetup(event);
          break;

        case MANAGE_GAME_STEPPER_STATES.PREVIEW:
          this.previewChanges.confirmAllChanges(event);
          break;

        default:
          break;
      }
    } else {
      this.gamesGrid.confirmGamesSelection(event);
    }
  }

  setSetupStep( { addedGames, removedGames } ) {
    this.addedGames = addedGames;
    this.removedGames = removedGames;

    let setupStepRequired = this.addedGames && this.addedGames.length > 0;
    let skipSetupStep = !this.addedGames || this.addedGames.length === 0;

    if (setupStepRequired) {
      this.disabled = true;
      this.stepper.next();
    } else if (skipSetupStep) {
      this.skipSetupStepAndSetPreview();
    }
  }

  setPreviewStep( { addedGames } ) {
    this.addedGames = addedGames;
    this.stepper.next();
  }

  saveSelectedGames( games ) {
    this._bulkSaveChanges(games);
  }

  goBack() {
    let backIndex = this.stepper.selectedIndex - 1;
    if (!this.addedGames || this.addedGames.length === 0) {
      backIndex = 0;
    }
    this.disabled = false;
    this.stepper.selectedIndex = backIndex;
  }

  previewStepSaveChanges() {
    if (this.addedGames && this.addedGames.length) {
      this.gameGroupFiltersService.getList(this.entity.path)
        .pipe(
          switchMap(( gameGroupFilters: GameGroupFilter[] ) => {
            let allGamesFiltersList = Array.isArray(gameGroupFilters) && gameGroupFilters.length
              && gameGroupFilters.filter(gameGroupFilter => Array.isArray(gameGroupFilter.games) && !gameGroupFilter.games.length);

            const [lastUpdatedFilter] = Array.isArray(allGamesFiltersList) && allGamesFiltersList.length ?
              allGamesFiltersList.sort(( a, b ) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()) :
              [];

            let specialGames: Game[] = this.addedGames.filter(( addedGame: Game ) => !isGameSupportGameGroupFilters(addedGame));

            if (Array.isArray(specialGames) && specialGames.length) {
              specialGames = specialGames.filter(( specialGame: GameInfo ) => specialGame.type === 'slot');
            }

            if (lastUpdatedFilter && lastUpdatedFilter.minBetWillIncreased) {
              const gamesWhichDoNotSupportMinBetIncreasing = this.addedGames.filter(( addedGame: Game ) => {
                return isGameSupportGameGroupFilters(addedGame) && !addedGame.features?.increaseMinBetSupported;
              });

              specialGames = [...specialGames, ...gamesWhichDoNotSupportMinBetIncreasing];
            }

            if (lastUpdatedFilter && lastUpdatedFilter.maxBetWillDecreased) {
              const gamesWhichDoNotSupportMaxBetDecreasing = this.addedGames.filter(( addedGame: Game ) => {
                return isGameSupportGameGroupFilters(addedGame) && !addedGame.features?.decreaseMaxBetSupported;
              });

              specialGames = [...specialGames, ...gamesWhichDoNotSupportMaxBetDecreasing];
            }

            return lastUpdatedFilter && specialGames.length ?
              this.dialog.open(AddSpecificGamesAlertDialogComponent,
                {
                  width: '600px',
                  data: specialGames,
                  disableClose: true,
                })
                .afterClosed()
                .pipe(
                  filter(isConfirmed => isConfirmed),
                ) :
              of(null);
          }),
        )
        .subscribe(
          () => {
            this.removeGames();
            this._saveCustomizedGames(this.addedGames);
          }
        );
    } else {
      this.removeGames();
    }
  }

  private removeGames() {
    if (this.removedGames && this.removedGames.length) {
      this._bulkSaveChanges({ removedGames: this.removedGames, addedGames: [] });
    }
  }

  private skipSetupStepAndSetPreview() {
    this.stepper.selectedIndex = this.stepper.steps.toArray()
      .findIndex(item => item.state === MANAGE_GAME_STEPPER_STATES.PREVIEW);
  }

  private _bulkSaveChanges( { addedGames, removedGames } ) {
    let addSlot$, addLive$, removeSlot$, removeLive$, apply$;

    if (addedGames && addedGames.length) {
      const addedSlot = addedGames.filter(game => !isLiveGame(game));
      const addedLive = addedGames.filter(game => isLiveGame(game));

      addSlot$ = addedSlot?.length ? this.service.setEntityGames(addedSlot.map(game => game.code), this.entity.path) : of([]);
      addLive$ = addedLive?.length ? this.service.setEntityLiveGames(addedLive.map(game => game.code), this.entity.path) : of([]);
    }

    if (removedGames && removedGames.length) {
      const removedSlot = removedGames.filter(game => !isLiveGame(game));
      const removedLive = removedGames.filter(game => isLiveGame(game));

      removeSlot$ = removedSlot?.length ? this.service.setEntityGames(removedSlot.map(game => game.code), this.entity.path, true) : of([]);
      removeLive$ = removedLive?.length
        ? this.service.setEntityLiveGames(removedLive.map(game => game.code), this.entity.path, true)
        : of([]);
    }

    apply$ = forkJoin([addSlot$ || of([]), addLive$ || of([]), removeSlot$ || of([]), removeLive$ || of([])]);

    if (apply$) {
      apply$
        .subscribe(() => this.dialogRef.close({ addedGames, removedGames }), this.onApplyError.bind(this));
    }
  }

  private onApplyError( { error } ) {
    let forceRequired = 'code' in error && error.code === ERROR_GAME_DELETE_NEED_FORCE_FLAG;

    if (forceRequired) {
      this.service.forceRemoveConfirmed
        .pipe(
          take(1)
        )
        .subscribe(() => {
          this.dialogRef.close();
        });
    }
  }

  private _saveCustomizedGames( addedGames: Game[] ): void {
    const rejectedGames = [];
    const sources: Observable<Game>[] = addedGames.map(( game ) => {
      const { code, status, settings, externalGameId } = game;
      const body: ChangeEntityGameData = { status, externalGameId };

      if (!!settings && typeof settings === 'object' && Object.keys(settings).length > 0) {
        Object.assign(body, { settings });
      }

      return this.service
        .postGameDetails(code, this.entity.path, isLiveGame(game), body)
        .pipe(
          catchError(( err ) => {
            rejectedGames.push(game);
            this.manageGamesService.setGameAsRejected(game, err);
            return of(err);
          })
        );
    });

    this.disabled = true;

    from(sources).pipe(concatAll())
      .pipe(
        finalize(() => {
          this.disabled = false;
          this.dialogRef.close({
            addedGames: addedGames.filter(game => rejectedGames.indexOf(game) === -1)
          });
        })
      )
      .subscribe(
        ( game ) => {
          this.manageGamesService.setGameAsAdded(game);
        }
      );
  }

  private setDialogData( data: ManageGamesDialogData ) {
    this.entity = data.entity;
    this.entityGames = data.entityGames;
    this.allowFullManagement = data.allowFullManagement;
  }
}
