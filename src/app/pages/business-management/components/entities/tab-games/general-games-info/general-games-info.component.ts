import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  PERMISSIONS_NAMES,
  RowAction,
  SwDexieService,
  SwHubAuthService,
  SwuiGridComponent,
  SwuiGridDataService,
  SwuiGridField,
  SwuiNotificationsService,
  SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import moment from 'moment';
import { forkJoin, from, Observable, of, Subject, throwError } from 'rxjs';
import { catchError, filter, finalize, map, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { Entity } from '../../../../../../common/models/entity.model';
import { GameGroup } from '../../../../../../common/models/game-group.model';
import { CurrencyService } from '../../../../../../common/services/currency.service';
import { GameGroupService } from '../../../../../../common/services/game-group.service';
import { GameProviderService } from '../../../../../../common/services/game-provider.service';
import { GAME_STATUSES, GameService } from '../../../../../../common/services/game.service';
import { LabelsService } from '../../../../../../common/services/labels.service';
import { LanguagesService } from '../../../../../../common/services/languages.service';
import { Currency, Game, isLiveGame, Language } from '../../../../../../common/typings';
import { Label } from '../../../../../../common/typings/label';
import { SetupEntityService } from '../../setup-entity.service';
import { GameForceRemoveDialogComponent, GameForceRemoveDialogData } from '../dialogs/game-force-remove-dialog.component';
import { GameSettingsToRunComponent } from '../dialogs/game-settings-to-run.component';
import { ManageGamesDialogComponent, ManageGamesDialogData, ManageGamesDialogResult } from '../dialogs/manage-games-dialog.component';
import { GamesRefreshService } from '../games-refresh.service';
import { SCHEMA_FILTER, SCHEMA_LIST } from './games.schema';

@Component({
    selector: 'general-games-info',
    templateUrl: './general-games-info.component.html',
    styleUrls: ['./general-games-info.component.scss'],
    providers: [
        GameProviderService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: GameService },
    ],
    standalone: false
})
export class GeneralGamesInfoComponent implements OnInit {
  @Input() allowFullManagement: boolean;

  @Input()
  set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
  }

  get entity(): Entity {
    return this._entity;
  }

  schema: SwuiGridField[] = SCHEMA_LIST;
  filterSchema: SwuiGridField[] = SCHEMA_FILTER;
  schemaItems = [];
  actions: RowAction[];
  loading: boolean = false;
  isSuperAdmin: boolean = true;
  languages?: Language[];
  currencies?: Currency[];
  gameGroups: GameGroup[];

  entityGames: Game[];
  schemaTypeName = 'entity-setup-games-tab';
  runAvailable = false;

  @ViewChild(SwuiGridComponent, { static: true }) gridRef?: SwuiGridComponent<Game>;

  private _entity: Entity;
  private readonly destroyed$ = new Subject<void>();

  constructor( private service: GameService,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               private dialog: MatDialog,
               private swHubAuthService: SwHubAuthService,
               private gameLabelsService: LabelsService,
               private setupService: SetupEntityService,
               private gamesRefreshService: GamesRefreshService,
               private languagesService: LanguagesService<Language>,
               private currencyService: CurrencyService<Currency>,
               private gameGroupService: GameGroupService,
               private dexieService: SwDexieService
  ) {
    this.isSuperAdmin = this.swHubAuthService.isSuperAdmin;
    this.filterSchema = this.initFieldsFilterData(this.setupService.availableProviders, 'providerId');

    this.gameLabelsService.getGameLabels().pipe(
      take(1))
      .subscribe(( labels: Label[] ) => {
        this.filterSchema = this.initFieldsFilterData(labels, 'labelsId');
      });

    if (!this.isSuperAdmin) {
      this.filterSchema = this.filterSchema.filter(schemaFields => schemaFields.field !== 'royalties');
    }
  }

  ngOnInit() {
    this.runAvailable = this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_URL]) && this.entity.type !== 'entity';
    forkJoin([
      this.languagesService.getList(undefined, this.entity.path),
      this.currencyService.getList(undefined, this.entity.path),
      this.gameGroupService.getGameGroupsList(this.entity.path)
    ])
      .pipe(take(1))
      .subscribe(( [languages, currencies, gameGroups] ) => {
        this.languages = languages;
        this.currencies = currencies;
        this.gameGroups = gameGroups;
      });

    this.gamesRefreshService.listen('jp-info')
      .subscribe(() => {
        this.gridRef.dataSource.loadData();
      });

    this.gridRef.dataSource.requestData = {
      path: this.entity.path,
      isSuperAdmin: this.isSuperAdmin,
      changeStateDisabled: this.swHubAuthService.areGranted([
        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,
        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_DISABLED
      ]),
      changeStateEnabled: this.swHubAuthService.areGranted([
        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,
        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_ENABLED
      ]),
      changeStateLiveDisabled: this.swHubAuthService.areGranted([
        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,
        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_DISABLED
      ]),
      changeStateLiveEnabled: this.swHubAuthService.areGranted([
        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,
        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_ENABLED
      ]),
      changeState: this.swHubAuthService.areGranted([
        PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE
      ]),
      changeStateLive: this.swHubAuthService.areGranted([
        PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE
      ])
    };

    this.setActions();
    this.initStreams();
    this.checkAndModifyScheme();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initStreams() {
    this.service._allGames
      .pipe(takeUntil(this.destroyed$)).subscribe(( games: Game[] ) => {
      this.entityGames = games;
    });

    this.service.forceRemoveConfirm
      .pipe(takeUntil(this.destroyed$))
      .subscribe(code => this.showForceRemoveConfirmation(code));
  }

  checkStringBoolean( val: any ) {
    switch (val) {
      case 'true':
        return true;
      case 'false':
        return false;
      default:
        return val;
    }
  }

  showManageGamesModal() {
    this.service.getAllGames(this.entity.path, true, true).pipe(
      switchMap(( games: Game[] ) => this.dialog.open(ManageGamesDialogComponent, {
          width: '1200px',
          maxHeight: '90vh',
          data: <ManageGamesDialogData>{
            entity: this.entity,
            entityGames: games,
            allowFullManagement: this.allowFullManagement
          },
          disableClose: true
        }).afterClosed()
      )
    ).pipe(
      filter(( result ) => typeof result !== 'undefined'),
      tap(( result: ManageGamesDialogResult ) => this.onGamesAppliedFn(result)),
      switchMap(() => this.service.getAllGames(this.entity.path, true, true)),
      map(( games: Game[] ) => {
        const changeState = this.swHubAuthService.areGranted([
          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE
        ]);
        const changeStateDisabled = this.swHubAuthService.areGranted([
          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,
          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_DISABLED
        ]);
        const changeStateEnabled = this.swHubAuthService.areGranted([
          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,
          PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE_ENABLED
        ]);
        const changeStateLive = this.swHubAuthService.areGranted([
          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE
        ]);
        const changeStateLiveDisabled = this.swHubAuthService.areGranted([
          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,
          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_DISABLED
        ]);
        const changeStateLiveEnabled = this.swHubAuthService.areGranted([
          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE,
          PERMISSIONS_NAMES.ENTITY_LIVEGAME_CHANGE_STATE_ENABLED
        ]);

        return games.map(game => {
          game._meta = {
            changeState,
            changeStateLive,
            isSuperAdmin: this.isSuperAdmin,
            changeStateDisabled: changeStateDisabled,
            changeStateEnabled: changeStateEnabled,
            changeStateLiveDisabled,
            changeStateLiveEnabled
          };
          return game;
        });
      }),
      takeUntil(this.destroyed$),
      finalize(() => {
        this.gridRef.dataSource.loadData();
        this.gamesRefreshService.refresh('general');
      })
    ).subscribe(( games: Game[] ) => this.entityGames = games);
  }

  onWidgetActionFn( { field, row, payload } ) {
    switch (field) {
      case 'status':
        this.handleWidgetStatusAction(row, payload);
        break;

      case 'royalties':
        this.handleRoyaltiesAction(row, payload);
        break;

      default:
        break;
    }
  }

  onGamesAppliedFn( { addedGames, removedGames }: ManageGamesDialogResult ) {
    if (addedGames && addedGames.length) {
      this.showApplyNotification(addedGames.length);
    }
    if (removedGames && removedGames.length) {
      this.showApplyNotification(removedGames.length, true);
    }
  }

  removeGames(gameCode: string, force: boolean = false, isLive = false) {
    const req$ = isLive
      ? this.service.removeEntityLiveGame(gameCode, this.entity.path, force)
      : this.service.removeEntityGame(gameCode, this.entity.path, force);

    req$
      .pipe(
        takeUntil(this.destroyed$),
        tap(() => this.showApplyNotification(1, true, force))
      ).subscribe(() => {
        this.gridRef.dataSource.loadData();
        this.gamesRefreshService.refresh('general');
      }
    );
  }

  downloadCsv() {
    this.loading = true;
    const fileName = `${this.entity.name} Export games list ${moment().format('YYYY-MM-DD HH:MM')}`;
    this.service.downloadCsv(this.entity.path, fileName).pipe(
      catchError(( err ) => {
        this.loading = false;
        return throwError(err);
      }),
      take(1)
    ).subscribe(() => {
      this.loading = false;
    });
  }

  setRunSettings() {
    from(this.dexieService.getRunSettings(this.entity.path))
      .pipe(
        switchMap(data => this.dialog.open(GameSettingsToRunComponent, {
          data: {
            languages: this.languages,
            currencies: this.currencies,
            gameGroups: this.gameGroups,
            language: this.entity.defaultLanguage,
            currency: this.entity.defaultCurrency,
            state: data
          }
        })
          .afterClosed()),
        filter(data => !!data),
        take(1)
      )
      .subscribe(data => {
        this.dexieService.putRunSettings(this.entity.path, data);
      });
  }

  private showApplyNotification( amount: number, removed: boolean = false, _: boolean = false ) {
    let single = amount === 1;
    let message;
    if (removed) {
      if (single) {
        message = 'ENTITY_SETUP.GAMES.notificationSingleGameRemoved';
      } else {
        message = 'ENTITY_SETUP.GAMES.notificationMultipleGamesRemoved';
      }
    } else {
      if (single) {
        message = 'ENTITY_SETUP.GAMES.notificationSingleGameAdded';
      } else {
        message = 'ENTITY_SETUP.GAMES.notificationMultipleGamesAdded';
      }
    }
    this.translate.get(message, { amount: amount })
      .subscribe(msg => this.notifications.success(msg, ''));
  }

  private refreshGames() {
    this.gridRef.loading$.next(true);
    this.gridRef.dataSource.loadData();
  }

  private setActions() {
    this.actions = [];

    if (this.runAvailable) {
      this.actions.push(new RowAction({
        icon: 'play_arrow',
        title: 'Play fun game',
        inMenu: false,
        fn: ( row ) => {
          this.runGame(row);
        },
        canActivateFn: ( { status } ) => ['normal', 'test'].includes(status)
      }));
    }

    this.actions.push(new RowAction({
      icon: 'delete',
      title: 'Remove',
      inMenu: false,
      fn: ( row ) => this.removeGames(row.code, false, isLiveGame(row)),
      canActivateFn: ( row: Game ) => !isLiveGame(row)
        ? this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE])
        : this.swHubAuthService.areGranted([PERMISSIONS_NAMES.ENTITY_LIVEGAME, PERMISSIONS_NAMES.ENTITY_LIVEGAME_REMOVE]),
    }));
  }

  private runGame( game: Game ) {
    from(this.dexieService.getRunSettings(this.entity.path))
      .pipe(
        switchMap(data => {
          if (data.doNotShow) {
            return of(data);
          }

          return this.dialog.open(GameSettingsToRunComponent, {
            data: {
              languages: this.languages,
              currencies: this.currencies,
              gameGroups: this.gameGroups,
              language: this.entity.defaultLanguage,
              currency: this.entity.defaultCurrency,
              state: data
            }
          })
            .afterClosed();
        }),
        take(1),
        filter(data => !!data),
        switchMap(data => {
          this.dexieService.putRunSettings(this.entity.path, data);

          const value = Object.entries(data).reduce(( res: any, [key, val] ) => {
            if (val && key !== 'doNotShow') {
              res[key] = val;
            }

            return res;
          }, {});

          const settings = {
            ...value,
            path: this.entity.path,
            gameCode: game.code
          };

          return this.service.getFunUrl(settings);
        })
      )
      .subscribe(url => {
        window.open(url, '_blank');
      });
  }

  private handleWidgetStatusAction( row: Game, payload: any ) {
    let statusGame;
    if (payload.status === GAME_STATUSES.HIDDEN) {
      statusGame = this.service.setStatus(row, payload.status, this._entity?.path);
    } else if (payload.status === GAME_STATUSES.TEST) {
      statusGame = this.service.setStatus(row, payload.status, this._entity?.path);
    } else if (payload.status === GAME_STATUSES.SUSPENDED) {
      statusGame = this.service.suspendedGame(this._entity?.path, row.code, true, isLiveGame(row));
    } else if (payload.status === GAME_STATUSES.NORMAL) {
      statusGame = this.service.unSuspendedGame(this._entity?.path, row.code, isLiveGame(row));
    } else if (payload.status === GAME_STATUSES.KILL_SESSION) {
      statusGame = this.setStatusWithoutKilling(row);
    }

    statusGame.pipe(
      map(( response: Game ) => Object.assign(row, response)),
      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.WHITELISTING.notificationChanged'))),
      takeUntil(this.destroyed$),
      finalize(() => {
        this.refreshGames();
        this.gamesRefreshService.refresh('general');
        payload.onCompleteFn();
      })
    ).subscribe();
  }

  private setStatusWithoutKilling( row: Game ): Observable<any> {
    return this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      data: { message: 'ENTITY_SETUP.GAMES.MODALS.kill_sessions' },
      disableClose: true
    }).afterClosed().pipe(
      filter(val => !!val),
      switchMap(() => this.service.suspendedGame(this._entity?.path, row.code, false, isLiveGame(row)))
    );
  }

  private handleRoyaltiesAction( row: Game, payload: any ) {
    let royalties = parseFloat(parseFloat(payload.value).toFixed(2)) / 100;
    let sub = this.service.updateRoyalties(row.code, this.entity.path, isLiveGame(row), royalties).subscribe(
      () => {
      },
      () => {
      },
      () => {
        payload.onCompleteFn();
        sub.unsubscribe();
      }
    );
  }

  private checkAndModifyScheme() {
    this.schemaItems = this.schema.filter(item => item.isList);
    let exclude = [];

    if (!this.allowFullManagement) {
      exclude.push('royalties');
    }

    if (!this.entity?.jurisdiction || !this.entity?.jurisdiction[0] || this.entity?.jurisdiction[0].code !== 'IT') {
      exclude.push('aamsCode');
      exclude.push('mustWinJackpotBundled');
    }

    this.schemaItems = [...this.schemaItems.filter(item => exclude.indexOf(item.field) === -1)];
  }

  private showForceRemoveConfirmation( code: string ) {
    this.dialog.open(GameForceRemoveDialogComponent, {
      width: '600px',
      data: <GameForceRemoveDialogData>{
        entity: this.entity,
        games: this.entityGames.filter(game => code === game.code),
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(data => !!data),
      take(1),
    ).subscribe(( { slot, live } ) => {
      if (slot && slot.length) {
        this.removeGames(slot, true);
      }

      if (live && live.length) {
        this.removeGames(live, true, true);
      }
    });
  }

  private initFieldsFilterData( items: any[], fieldName: string ): SwuiGridField[] {
    this.filterSchema.find(schemaFields => schemaFields.field === fieldName).data =
      items?.map(( item ) => ({ id: item.id, text: item.title }));
    return this.filterSchema;
  }
}
