import { AfterViewInit, Component, ElementRef, Inject, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';

@Component({
    selector: 'view-details',
    templateUrl: './view-details.component.html',
    standalone: false
})
export class ViewDetailsComponent implements AfterViewInit {
  area: FormControl = new FormControl('');
  data: any;
  @ViewChild('textArea') textArea: ElementRef;

  constructor( @Inject(MAT_DIALOG_DATA) { data }: any,
               public dialogRef: MatDialogRef<ViewDetailsComponent>,
               private translate: TranslateService,
               private notificationsService: SwuiNotificationsService,
  ) {
    this.data = data;
    this.area.setValue(JSON.stringify(data, null, 4));
  }

  ngAfterViewInit() {
    this.textArea.nativeElement.setSelectionRange(0, 0);
  }

  getCopyContentFn() {
    return () => this.area.value;
  }

  copySuccess() {
    this.translate.get('INTEGRATIONS.notificationCopy')
      .subscribe(message => this.notificationsService.success(message, ''));
  }
}
