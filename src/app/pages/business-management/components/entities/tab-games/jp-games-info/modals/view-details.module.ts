import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LayoutModule } from '@angular/cdk/layout';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { ClipboardModule } from '../../../../../../../common/components/clipboard/clipboard.module';
import { ViewDetailsComponent } from './view-details.component';

@NgModule({
  declarations: [
    ViewDetailsComponent
  ],
  imports: [
    CommonModule,
    MatCardModule,
    ReactiveFormsModule,
    ClipboardModule,
    TranslateModule,
    LayoutModule,
    MatButtonModule,
    MatDialogModule
  ]
})
export class ViewDetailsModule {
}
