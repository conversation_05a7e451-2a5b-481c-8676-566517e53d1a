import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { Entity } from '../../../../../../../common/models/entity.model';
import { Game } from '../../../../../../../common/typings';
import { SetupGameProgressComponent } from '../setup-game-progress.component';
import { SetupGameItem } from '../setup-game.model';

@Component({
    selector: 'manage-games-setup',
    templateUrl: './manage-games-setup.component.html',
    styleUrls: ['./manage-games-setup.component.scss'],
    standalone: false
})
export class ManageGamesSetupComponent {

  @ViewChild(SetupGameProgressComponent, { static: true }) public progress: SetupGameProgressComponent;

  @Input() public entity: Entity;
  @Input() public entityGames: Game[];

  @Output() public applyButtonDisable: EventEmitter<boolean> = new EventEmitter();
  @Output() public gamesSetupComplete: EventEmitter<any> = new EventEmitter();

  public setupGameItems: SetupGameItem[];
  public selectedItem: SetupGameItem;

  private _addedGames: Game[];
  private _gamesSubmitted: string[] = [];

  @Input()
  set addedGames( value: Game[] ) {
    if (!value) return;
    this._addedGames = value;

    this.setupGameItems = [];
    value.forEach(( game: Game ) => {
      let item = new SetupGameItem(game);
      [...new Set(this._gamesSubmitted)].find(gameSubmitted => {
        if (game.code === gameSubmitted) {
          item.complete = true;
        }
      });
      this.setupGameItems.push(item);
    });

    this.setNextItem();
  }

  get addedGames(): Game[] {
    return this._addedGames;
  }

  constructor() {
  }

  ngOnDestroy() {
    this._gamesSubmitted = [];
  }

  public isItemSelected( item: SetupGameItem ) {
    return item === this.selectedItem;
  }

  public selectItem( item ) {
    this.selectedItem = item;
    item.unviewed = false;
  }

  public onGameSubmit( formValues ) {
    this.selectedItem.setChanges(formValues);
    this._gamesSubmitted.push(this.selectedItem.game.code);
    this.progress.updateProgress();
    this.setNextItem();
  }

  public setNextItem() {
    let idx = this.setupGameItems.indexOf(this.selectedItem);
    idx++;

    if (this.setupGameItems[idx]) {
      this.selectItem(this.setupGameItems[idx]);
    }
  }

  public onProgressFinished() {
    this.applyButtonDisable.emit(false);
  }

  public completeGamesSetup( event ) {
    event.preventDefault();

    let addedGames = this.setupGameItems
      .map(( item: SetupGameItem ) => item.getGameWithChanges());

    this.gamesSetupComplete.emit({ addedGames });
  }

  public setAllGamesAsComplete() {
    this.setupGameItems.forEach(item => {
      if (!item.required) {
        item.complete = true;
        item.unviewed = false;
      }
    });
    this.progress.updateProgress();
  }
}
