import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { Game } from '../../../../../../common/typings';
import { Entity } from '../../../../../../common/models/entity.model';

@Component({
    selector: 'setup-game-form',
    templateUrl: './setup-game-form.component.html',
    standalone: false
})
export class SetupGameFormComponent implements OnInit {

  @Input() public entity: Entity;

  @Output() public gameSubmit: EventEmitter<Game> = new EventEmitter();

  public form: FormGroup;
  public aamsCode = new FormControl();
  public mustWinJackpotBundled = new FormControl();

  public submitted: boolean = false;

  public isItalianRegulation = false;

  private _game: Game;
  @Input()
  set game( value: Game ) {
    const gameAlreadySet = this._game && this._game.code === value.code;
    if (!value || gameAlreadySet) return;

    this._game = value;
    let settings;

    if ('settings' in value && value.settings !== null && typeof value.settings === 'object') {
      settings = JSON.stringify(value.settings, null, '  ');
    }

    const formValue = {
      status: value.status,
      externalGameId: value.externalGameId,
      settings,
    };

    this.form.patchValue(formValue);
  }

  get game(): Game {
    return this._game;
  }

  constructor( public fb: FormBuilder,
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.isItalianRegulation = this.entity?.jurisdiction && this.entity?.jurisdiction[0]
      ? this.entity.jurisdiction[0].code === 'IT'
      : false;

    if (this.isItalianRegulation) {
      this.aamsCode.setValidators(Validators.required);
    }
  }

  public submitChanges( event: Event ) {
    event.preventDefault();

    this.submitted = true;

    if (this.form.valid && this.aamsCode.valid) {
      let data = JSON.parse(JSON.stringify(this.form.value));

      if (!data.externalGameId) {
        delete data.externalGameId;
      }

      if ('settings' in data) {
        // settings will be as string because of textarea value
        if (data.settings !== 'null' && data.settings !== '' && typeof data.settings === 'string') {
          Object.assign(data, { settings: JSON.parse(data.settings) });
        } else {
          delete data.settings;
        }
      }

      if (this.isItalianRegulation) {
        if (data.settings) {
          data.settings.aamsCode = this.aamsCode.value.toString();
          data.settings.mustWinJackpotBundled = this.mustWinJackpotBundled.value;
        } else {
          data.settings = { aamsCode: this.aamsCode.value.toString(), mustWinJackpotBundled: this.mustWinJackpotBundled.value };
        }

        if (!this.mustWinJackpotBundled.value) {
          delete data.settings.mustWinJackpotBundled;
        }
      }

      this.gameSubmit.emit(data);
    }
  }

  get externalGameIdControl(): FormControl {
    return this.form?.get('externalGameId') as FormControl;
  }

  private initForm() {
    this.form = this.fb.group({
      status: ['', Validators.required],
      settings: ['', ValidationService.IfNotEmpty(ValidationService.JSONValidator)],
      externalGameId: [''],
    });
  }
}
