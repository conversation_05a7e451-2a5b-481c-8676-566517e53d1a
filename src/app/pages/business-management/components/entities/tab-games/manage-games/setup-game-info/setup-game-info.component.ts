import { Component, Input } from '@angular/core';

import { providerCodeClassMap, tagClassMap } from '../../../../../../../app.constants';
import { Game, GameLabel } from '../../../../../../../common/typings';


@Component({
    selector: 'setup-game-info',
    templateUrl: './setup-game-info.component.html',
    styleUrls: ['./setup-game-info.component.scss'],
    standalone: false
})

export class SetupGameInfoComponent {

  private _game: Game;

  @Input()
  set game( value: Game ) {
    this._game = value;
  }

  get game(): Game {
    return this._game;
  }

  constructor() {

  }


  public getProviderClass( providerCode: string ): string {
    let cssClass = providerCodeClassMap.DEFAULT;

    if (providerCodeClassMap.hasOwnProperty(providerCode)) {
      cssClass = providerCodeClassMap[providerCode];
    }

    return cssClass;
  }

  public getLabelClass( label: GameLabel ): string {
    let cssClass = 'border-left-grey';

    if (tagClassMap.hasOwnProperty(label.group)) {
      cssClass = tagClassMap[label.group];
    }

    return cssClass;
  }
}
