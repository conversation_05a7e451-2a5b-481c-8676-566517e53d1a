import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SetupGameItem } from './setup-game.model';

@Component({
    selector: 'setup-game-progress',
    templateUrl: './setup-game-progress.component.html',
    standalone: false
})
export class SetupGameProgressComponent {

  @Output() public progressFinished: EventEmitter<boolean> = new EventEmitter();

  public finished: boolean = false;
  public percent: number = 0;
  public total: number = 0;
  public completed: number = 0;

  private _setupGameItems: SetupGameItem[];
  @Input()
  set setupGameItems( value: SetupGameItem[] ) {
    if (!value) return;

    this._setupGameItems = value;

    this.total = value.length;
    this.updateProgress();
  }

  get setupGameItems(): SetupGameItem[] {
    return this._setupGameItems;
  }

  updateProgress() {
    this.calculateCompletedItems();
    this.calculateTotalPercent();
    this.checkFinished();
  }

  calculateCompletedItems() {
    this.completed = this._setupGameItems ? this._setupGameItems.filter(item => item.complete).length : 0;
    this.finished = this.completed === this.total;
  }

  calculateTotalPercent() {
    let percent = 0;

    if (!this.finished) {
      if (this.total > 0) {
        percent = (this.completed / this.total) * 100;
      }
    } else if (this.finished) {
      percent = 100;
    }

    this.percent = percent;
  }

  checkFinished() {
    if (this.finished) {
      this.progressFinished.emit(this.finished);
    }
  }
}
