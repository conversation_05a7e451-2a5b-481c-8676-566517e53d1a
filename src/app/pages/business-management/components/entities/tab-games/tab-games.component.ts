import { Component } from '@angular/core';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { Entity } from '../../../../../common/models/entity.model';
import { SetupEntityService } from '../setup-entity.service';

@Component({
    selector: 'tab-games',
    templateUrl: './tab-games.component.html',
    styleUrls: ['./tab-games.component.scss'],
    standalone: false
})
export class TabGamesComponent {
  readonly entity?: Entity;
  readonly isSuperadmin: boolean;

  constructor(
    { entity }: SetupEntityService,
    { isSuperAdmin }: SwHubAuthService,
  ) {
    this.isSuperadmin = isSuperAdmin;
    this.entity = entity;
  }
}
