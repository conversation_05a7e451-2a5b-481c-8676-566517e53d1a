import { Component } from '@angular/core';

import { Entity } from '../../../../../common/models/entity.model';
import { SetupEntityService } from '../setup-entity.service';


@Component({
    selector: 'tab-players',
    templateUrl: 'tab-players.component.html',
    styleUrls: ['./tab-players.component.scss'],
    standalone: false
})

export class TabPlayersComponent {
  readonly entity?: Entity;

  constructor( { entity }: SetupEntityService ) {
    this.entity = entity;
  }
}
