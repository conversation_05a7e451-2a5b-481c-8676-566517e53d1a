import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { EntityCountriesComponent, EntityCountry } from './entity-countries.component';
import { filter, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { EntitySettingsModel } from 'src/app/common/models/entity-settings.model';
import { getInheritedCountryCode } from 'src/app/common/typings';
import { CountryItem } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';
import { RowAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { EntitySettingsService } from 'src/app/common/services/entity-settings.service';
import { Entity } from '../../../../../common/models/entity.model';
import { combineLatest, forkJoin } from 'rxjs';
import { SetupEntityService } from '../setup-entity.service';

@Component({
    selector: '[entity-blocked-countries]',
    templateUrl: './entity-countries.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class EntityBlockedCountriesComponent extends EntityCountriesComponent {
  title = 'ENTITY_SETUP.REGIONAL.titleBlockedCountries';

  private blockedCountries: EntityCountry[] = [];

  constructor(
    dialog: MatDialog,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
    private setupEntityService: SetupEntityService,
    cdr: ChangeDetectorRef,
  ) {
    super(dialog, cdr);
  }

  ngOnInit() {
    combineLatest([
      this.entitySettingsService.getSettings(this.entity.path, true),
      this.setupEntityService.settings$
    ]).pipe(
      take(1)
    ).subscribe(([settingsOwn, settingsInherited]) => {
      this.blockedCountries = this.buildBlockedCountries(settingsOwn, settingsInherited);
      this.refreshData();
    });

    this.setupEntityService.entityNeedUpdate$.pipe(
      switchMap(() => forkJoin([
        this.entitySettingsService.getSettings(this.entity.path, true),
        this.entitySettingsService.getSettings(this.entity.path)
      ])),
      takeUntil(this.destroyed$),
    ).subscribe(([settingsOwn, settingsInherited]) => {
      this.blockedCountries = this.buildBlockedCountries(settingsOwn, settingsInherited);
      this.refreshData();
    });
  }

  showCountryModal($event: Event): void {
    super.showCountryModal($event);
    const source = this.buildCountriesModal('blocked');
    source.pipe(
      filter((data: { entity: Entity, selected: string[] }) => !!data && typeof data.entity !== 'undefined'),
      switchMap(({ entity, selected }) => {
        selected = selected.length ? selected : null;
        return this.entitySettingsService.patchSettings({ merchantGameRestrictionsUseIpCountries: selected }, entity.path);
      }),
      tap((settings: EntitySettingsModel) => this._entitySettings = settings),
      switchMap(() => this.entitySettingsService.getSettings(this.entity.path, true)),
      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountriesAdded'))),
      take(1)
    ).subscribe((ownEntitySettings: EntitySettingsModel) => {
      this.blockedCountries = this.buildBlockedCountries(ownEntitySettings, this._entitySettings);
      this.refreshData();
    });
  }

  removeCountryModal(countryCode: string) {
    const removeDialog = this.buildRemoveCountryModal(countryCode);
    removeDialog.pipe(
      filter(code => !!code && code in this.countriesHash),
      switchMap((code: string) => {
        let removed = this.blockedCountries.filter(country => country.code !== code);
        removed = removed.length ? removed : null;
        return this.entitySettingsService.patchSettings({ merchantGameRestrictionsUseIpCountries: removed }, this.entity.path);
      }),
      tap((settings: EntitySettingsModel) => this._entitySettings = settings),
      switchMap(() => this.entitySettingsService.getSettings(this.entity.path, true)),
      tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.REGIONAL.MODALS.notificationCountryRemoved'))),
      take(1),
    ).subscribe((ownEntitySettings) => {
      this.blockedCountries = this.buildBlockedCountries(ownEntitySettings, this._entitySettings);
      this.refreshData();
    });
  }

  getRowActions(country: EntityCountry): RowAction[] {
    if (country.isInherited) {
      return [];
    }
    return this.rowActions;
  }

  protected setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.actionDelete',
        icon: 'delete',
        inMenu: true,
        fn: ({ code }) => this.removeCountryModal(code)
      })
    ];
  }

  protected loadData(): EntityCountry[] {
    return this.blockedCountries;
  }

  protected buildAvailableCountries(): CountryItem[] {
    return Object.values(this.countriesHash).map((country) => {
      const item = new CountryItem(country);
      item.selected = this.blockedCountries.findIndex(({ code }) => code === item.code) > -1;
      if (item.code === this.entity.defaultCountry) {
        item.disabled = true;
      }
      return item;
    });
  }

  private buildBlockedCountries(settingsOwn: EntitySettingsModel, settingsInherited: EntitySettingsModel) {
    const ownCountries = settingsOwn.merchantGameRestrictionsUseIpCountries ?? [];
    const inheritedCountries = settingsInherited.merchantGameRestrictionsUseIpCountries ?? [];
    return getInheritedCountryCode(ownCountries, inheritedCountries).map<EntityCountry>(({ code, inherited }) => ({
      code,
      displayName: this.countriesHash[code]?.displayName ?? '',
      isDefault: false,
      isInherited: inherited
    }));
  }
}
