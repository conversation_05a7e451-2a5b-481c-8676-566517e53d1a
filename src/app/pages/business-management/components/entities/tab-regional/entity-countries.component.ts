import { ChangeDetectorRef, Directive, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { RowAction } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { codeArrayToObjectReducer } from '../../../../../common/services/entity.service';
import { Country } from '../../../../../common/typings';
import { CountryItem, MatCountryDialogComponent, MatCountryDialogData } from '../../mat-business-structure/dialogs/mat-country-dialog/mat-country-dialog.component';
import { RemoveConfirmDialogComponent } from '../dialogs/remove-confirm-dialog.component';
import { StructureEntityModel } from '../../mat-business-structure/structure-entity.model';

export interface EntityCountry {
  code: string;
  displayName: string;
  isDefault: boolean;
  isInherited: boolean;
}

@Directive()
export abstract class EntityCountriesComponent implements OnInit {
  searchControl: FormControl = new FormControl();
  rowActions: RowAction[];
  dataSource: MatTableDataSource<EntityCountry>;

  @Input() set disabledList(val: boolean) {
    this._disabledList = val;
    this.refreshData();
  }

  get disabledList(): boolean {
    return this._disabledList;
  }

  @Input() entity: StructureEntityModel;

  abstract title: string;

  @ViewChild('manageCountryBtn') private manageCountryBtn: ElementRef;


  @Input()
  set countries(countries: Country[]) {
    this._countries = countries;
    this.countriesHash = this._countries.reduce(codeArrayToObjectReducer, {});
  }

  get countries(): Country[] {
    return this._countries;
  }

  protected countriesHash: Object;
  protected _entitySettings: EntitySettingsModel;
  protected readonly destroyed$ = new Subject<void>();

  private _countries: Country[];
  private _disabledList: boolean;

  constructor(private dialog: MatDialog, private cdr: ChangeDetectorRef) {
    this.setRowActions();
  }

  ngOnInit() {
    this.refreshData();

    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((filterValue: string) => {
      this.dataSource.filter = filterValue?.trim().toLowerCase();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get displayedColumns(): string[] {
    return ['displayName', 'status', ...(this.disabledList ? [] : ['code'])];
  }

  showCountryModal($event: Event): void {
    $event.preventDefault();
    this.searchControl.reset();
  }

  abstract removeCountryModal(countryCode: string): void;

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  getRowActions(country: EntityCountry): RowAction[] {
    if (this.disabledList || country.isInherited) {
      return [];
    }
    return this.rowActions;
  }

  protected buildCountriesModal(countriesType: 'allowed' | 'restricted' | 'blocked') {
    const data: MatCountryDialogData = {
      countriesType,
      entity: this.entity,
      items: this.buildAvailableCountries()
    };
    return this.dialog.open(MatCountryDialogComponent, {
      disableClose: true,
      width: '500px',
      data
    }).afterClosed();
  }

  protected abstract buildAvailableCountries(): CountryItem[];

  protected buildRemoveCountryModal(countryCode: string) {
    return this.dialog.open(RemoveConfirmDialogComponent, {
      width: '500px',
      data: { removeCode: countryCode },
      disableClose: true
    }).afterClosed();
  }

  protected refreshData() {
    this.unFocusButton();
    this.dataSource = new MatTableDataSource(this.loadData());
    this.cdr.detectChanges();
  }

  protected abstract loadData(): EntityCountry[];

  protected abstract setRowActions(): void;

  private unFocusButton() {
    if (this.manageCountryBtn) {
      this.manageCountryBtn['_elementRef'].nativeElement.classList.remove('cdk-program-focused');
    }
  }
}
