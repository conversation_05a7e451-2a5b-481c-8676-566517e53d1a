import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { merge, Observable, of, Subject } from 'rxjs';
import { filter, finalize, share, switchMap, take, takeUntil } from 'rxjs/operators';

import { Entity } from '../../../../../common/models/entity.model';
import { JurisdictionService } from '../../../../../common/services/jurisdiction.service';
import { Jurisdiction } from '../../../../../common/typings/jurisdiction';
import {
  MatJurisdictionDialogData, MatJurisdictionsDialogComponent
} from '../../mat-business-structure/dialogs/mat-jurisdictions-dialog/mat-jurisdictions-dialog.component';
import { SetupEntityService } from '../setup-entity.service';


@Component({
    selector: '[entity-jurisdictions]',
    templateUrl: './entity-jurisdictions.component.html',
    standalone: false
})
export class EntityJurisdictionsComponent implements OnInit {

  @Input() entity: Entity;
  searchControl: FormControl = new FormControl();
  dataSource: MatTableDataSource<Jurisdiction>;
  entityJurisdictions: Jurisdiction[] = [];
  parentJurisdictions: Jurisdiction[] = [];
  masterJurisdictions: Jurisdiction[] = [];

  displayedColumns: string[] = ['title', 'code'];

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private dialog: MatDialog,
    private jurisdictionService: JurisdictionService,
    private authService: SwHubAuthService,
    private notificationsService: SwuiNotificationsService,
    private setupEntityService: SetupEntityService
  ) {
  }

  ngOnInit(): void {
    if (!this.entity || !this.entity.entityParent) {
      return;
    }

    this.useRouteResolvedData();
    this.fetchRemainingData();

    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( filterValue: string ) => {
      this.dataSource.filter = filterValue?.trim().toLowerCase();
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  showJurisdictionsModal() {
    this.searchControl.reset();
    this.dialog.open(MatJurisdictionsDialogComponent, {
      disableClose: true,
      width: '500px',
      data: <MatJurisdictionDialogData>{
        entity: this.entity,
        entityJurisdictions: this.entityJurisdictions,
        parentJurisdictions: this.parentJurisdictions,
        masterJurisdictions: this.masterJurisdictions,
      }
    }).afterClosed().pipe(
      filter(items => Array.isArray(items)),
      take(1)
    ).subscribe(( jurisdictionCodes: string[] ) => this.updateJurisdictions(jurisdictionCodes));
  }

  private useRouteResolvedData() {
    if (this.entity) {
      this.jurisdictionService.getEntityJurisdictions(this.entity.path).pipe(
        takeUntil(this.destroyed$)
      ).subscribe(data => {
        this.entityJurisdictions = data;
        this.dataSource = new MatTableDataSource(data);
      });
    }
  }

  private updateJurisdictions( updatedCodes: string[] ) {
    const codesBeforeUpdate = this.entityJurisdictions.map(i => i.code);
    const removedCodes = codesBeforeUpdate.filter(code => updatedCodes.indexOf(code) === -1);
    const addedCodes = updatedCodes.filter(code => codesBeforeUpdate.indexOf(code) === -1);

    const request = this.entity.isReseller() ? merge(
      this.removeJurisdictionCodes(removedCodes),
      this.addJurisdictionCodes(addedCodes)
    ) : this.addJurisdictionCodes(addedCodes);

    request.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(
      () => {
        this.setupEntityService.entityNeedUpdate$.next();
        this.notificationsService.success('Jurisdictions were changed successfully');
      }
    );
  }

  private fetchRemainingData() {
    this.jurisdictionService.getEntityJurisdictions(this.entity.entityParent.path)
      .pipe(take(1))
      .subscribe(( jurisdictions: Jurisdiction[] ) => this.parentJurisdictions = jurisdictions);

    if (this.authService.isSuperAdmin) {
      this.jurisdictionService.getEntityJurisdictions().pipe(take(1))
        .subscribe(( jurisdictions ) => this.masterJurisdictions = jurisdictions);
    }
  }

  private addJurisdictionCodes( addedCodes ): Observable<any> {
    return of(...addedCodes)
      .pipe(
        switchMap(( code ) => {
          const add$ = this.jurisdictionService.addEntityJurisdiction(this.entity.path, code).pipe(
            share(),
          );
          add$.pipe(take(1)).subscribe(() => {
            const added = this.parentJurisdictions.find(item => item.code === code);
            this.entityJurisdictions.push(added);
          });
          return add$;
        }),
        finalize(() => this.useRouteResolvedData())
      );
  }

  private removeJurisdictionCodes( removedCodes ): Observable<any> {
    return of(...removedCodes)
      .pipe(
        switchMap(( code: string ) => {
          const remove$ = this.jurisdictionService.deleteEntityJurisdiction(this.entity.path, code).pipe(
            share()
          );
          remove$.pipe(take(1)).subscribe(() => {
            const removedIndex = this.entityJurisdictions.findIndex(item => item.code === code);
            this.entityJurisdictions.splice(removedIndex, 1);
          });
          return remove$;
        }),
        finalize(() => this.useRouteResolvedData()),
      );
  }
}
