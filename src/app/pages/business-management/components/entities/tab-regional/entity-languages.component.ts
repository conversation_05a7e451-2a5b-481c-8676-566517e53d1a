import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, finalize, switchMap, take, takeUntil, tap } from 'rxjs/operators';

import { Entity } from '../../../../../common/models/entity.model';
import { codeArrayToObjectReducer, EntityService, pushDefaultToStart } from '../../../../../common/services/entity.service';
import { Language } from '../../../../../common/typings';
import { MatLanguageDialogComponent } from '../../mat-business-structure/dialogs/mat-language-dialog/mat-language-dialog.component';
import { PERMISSIONS_LIST } from '../../../../../app.constants';


interface EntityLanguage {
  code: string;
  displayName: string;
  isDefault: boolean;
}

@Component({
    selector: '[entity-languages]',
    templateUrl: './entity-languages.component.html',
    standalone: false
})
export class EntityLanguagesComponent implements OnInit, OnDestroy {
  @Input() public entity: Entity;
  searchControl: FormControl = new FormControl();
  dataSource: MatTableDataSource<EntityLanguage>;
  readonly rowActions: RowAction[];
  displayedColumns = ['displayName', 'code'];

  readonly allowedEdit: boolean;

  private languagesHash: { [code: string]: Language };
  private _languages: Language[] = [];

  @Input()
  set languages( languages: Language[] ) {
    this._languages = languages;
    this.languagesHash = this._languages.reduce(codeArrayToObjectReducer, {});
  }

  get languages(): Language[] {
    return this._languages;
  }

  private destroyed$ = new Subject<any>();

  constructor( private entityService: EntityService<Entity>,
               private authService: SwHubAuthService,
               private dialog: MatDialog,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
  ) {
    this.allowedEdit = this.authService.allowedTo(PERMISSIONS_LIST.ENTITY_SETTINGS);
    this.rowActions = this.allowedEdit ? [
      new RowAction({
        title: 'ENTITY_SETUP.REGIONAL.setDefault',
        icon: 'home',
        fn: ( { code } ) => this.setDefaultLanguage(code),
        canActivateFn: ( { isDefault } ) => !isDefault,
      }),
    ] : [];
  }

  ngOnInit(): void {
    this.refreshData();

    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( filterValue: string ) => {
      this.dataSource.filter = filterValue?.trim().toLowerCase();
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  showLanguageModal() {
    this.searchControl.reset();
    this.dialog.open(MatLanguageDialogComponent, {
      disableClose: true,
      width: '500px',
      data: { entity: this.entity, languages: this.languages }
    }).afterClosed()
      .pipe(
        filter(( data: { entity: Entity, selected: string[] } ) => !!data && typeof data.entity !== 'undefined'),
        take(1),
        switchMap(( { entity, selected } ) => {
          const updated = new Entity(entity);
          updated.languages = [...selected];

          return this.entityService.updateEntityItem(updated);
        }),
        tap(( entity: Entity ) => this.entity.languages = entity.languages),
        switchMap(() => this.translate.get('ENTITY_SETUP.REGIONAL.languagesWereAdded')),
        tap(( message: string ) => this.notifications.success(message)),
        finalize(() => this.refreshData())
      ).subscribe();
  }

  setDefaultLanguage( defaultLanguage: string ) {

    this.entity.defaultLanguage = defaultLanguage;
    this.entityService.updateEntityItem(this.entity)
      .pipe(
        take(1)
      ).subscribe(( data ) => {
      this.entity.update(data);
      this.refreshData();
    });
  }

  getLanguage( code: string ): Language {
    return this.languagesHash[code];
  }

  applyFilter( filterValue: string ) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  private refreshData() {
    const countries = pushDefaultToStart(this.entity ? this.entity.languages : [], this.entity?.defaultCurrency);

    const data: EntityLanguage[] = countries.map(( code ) => {
      const language = this.getLanguage(code.toLowerCase());
      return <EntityLanguage>{
        code,
        displayName: language.name,
        isDefault: this.entity.defaultLanguage === code
      };
    });
    this.dataSource = new MatTableDataSource(data);
  }
}
