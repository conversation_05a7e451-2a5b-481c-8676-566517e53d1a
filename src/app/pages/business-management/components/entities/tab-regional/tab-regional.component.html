<ng-container *ngIf="restrictedCountriesAvailable">
  <hints message="Only one list can be used at a time. Please select the list of 'Allowed' or 'Restricted' countries."></hints>
</ng-container>

<ng-container *ngIf="restrictedCountriesAvailable">
  <mat-form-field appearance="outline" class="width50" style="padding-right: 8px">
    <mat-label>{{ 'ENTITY_SETUP.REGIONAL.useJurisdictionsDataOnly' | translate }}</mat-label>
    <mat-select [formControl]="useJurisdictionsDataOnlyControl">
      <mat-option [value]="'inherited'">{{entitySettingsParent?.useCountriesFromJurisdiction ? 'Yes' : 'No'}} (Inherited)</mat-option>
      <mat-option *ngFor="let data of jurisdictionsData" [value]="data.id">
        {{data.text}}
      </mat-option>
    </mat-select>
  </mat-form-field>
</ng-container>

<ng-container *ngIf="entity">
  <div class="tables-row" style="margin-bottom: 32px; margin-top: 32px">
    <div entity-allowed-countries
         class="tables-row__item"
         [ngClass]="{ 'opacitedList': accessToManageCountries(restrictedCountries?.length > 0 ) }"
         [entity]="entity"
         [countries]="countries"
         [disabledList]="accessToManageCountries(restrictedCountries?.length > 0 )"></div>
    <div entity-restricted-countries
         class="tables-row__item"
         [ngClass]="{ 'opacitedList': accessToManageCountries(entity.countries?.length > 0) }"
         [entity]="entity"
         [countries]="countries"
         [disabledList]="accessToManageCountries(entity.countries?.length > 0)"
         (restrictedOwnList)="updateRestrictedCountries($event)"
         [restrictedCountries]="restrictedCountries"></div>
    <div entity-blocked-countries
         class="tables-row__item"
         [entity]="entity"
         [countries]="countries"
         [disabledList]="false"></div>
    <div entity-jurisdictions *ngIf="jurisdictionAllowed" class="tables-row__item" [entity]="entity"></div>
    <div entity-currencies class="tables-row__item" [entity]="entity" [currencies]="currencies"></div>
    <div entity-languages class="tables-row__item" [entity]="entity" [languages]="languages"></div>
    <div entity-deployment-groups *ngIf="deploymentAvailable" class="tables-row__item" [entity]="entity"
         [deploymentGroups]="deploymentGroups"></div>
  </div>
</ng-container>
