import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { PERMISSIONS_NAMES, SwHubAuthService } from '@skywind-group/lib-swui';
import { combineLatest, forkJoin, merge, Subject } from 'rxjs';
import { map, skip, switchMap, take, takeUntil } from 'rxjs/operators';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';

import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { EntityService } from '../../../../../common/services/entity.service';
import { Country, Currency, getInheritedCountryCode, InheritedCountryCode, Language } from '../../../../../common/typings';
import { DeploymentGroup } from '../../../../../common/typings/deployment-group';
import { Jurisdiction } from '../../../../../common/typings/jurisdiction';
import { SetupEntityService } from '../setup-entity.service';
import { PERMISSIONS_LIST } from '../../../../../app.constants';


@Component({
    selector: 'tab-regional',
    templateUrl: 'tab-regional.component.html',
    styleUrls: ['tab-regional.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class TabRegionalComponent implements OnInit, OnDestroy {
  restrictedCountries?: InheritedCountryCode[] = [];
  readonly entity?: Entity;
  readonly countries?: Country[];
  readonly currencies?: Currency[];
  readonly languages?: Language[];
  readonly deploymentGroups?: DeploymentGroup[];
  readonly jurisdictionsData = [
    { id: false, text: 'No' },
    { id: true, text: 'Yes' },
  ];

  jurisdiction: Jurisdiction;

  readonly isSuperAdmin: boolean;
  disableManageCountries: boolean = false;
  useJurisdictionsDataOnlyControl: FormControl = new FormControl(false);
  entitySettingsParent: EntitySettingsModel;
  readonly restrictedCountriesAvailable: boolean;
  readonly deploymentAvailable: boolean;
  readonly jurisdictionAllowed: boolean;

  private readonly destroyed$ = new Subject<void>();
  private readonly canEdit: boolean;

  constructor(
    { snapshot: { data: { countries, currencies, entityJurisdictions, languages, deploymentGroups } } }: ActivatedRoute,
    private setupEntityService: SetupEntityService,
    private entityService: EntityService<Entity>,
    private entitySettingService: EntitySettingsService<EntitySettingsModel>,
    private swHubAuthService: SwHubAuthService,
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
  ) {
    this.isSuperAdmin = this.swHubAuthService.isSuperAdmin;
    this.canEdit = this.swHubAuthService.allowedTo([PERMISSIONS_NAMES.ENTITY, PERMISSIONS_NAMES.ENTITY_EDIT]);
    this.restrictedCountriesAvailable = this.swHubAuthService.allowedTo([PERMISSIONS_NAMES.RESTRICTED_COUNTRIES_SOLUTION]);
    this.deploymentAvailable = this.swHubAuthService.allowedTo([PERMISSIONS_NAMES.DEPLOYMENT]) && this.swHubAuthService.isSuperAdmin;
    this.jurisdictionAllowed = this.swHubAuthService.allowedTo(PERMISSIONS_LIST.JURISDICTION_VIEW);
    this.countries = countries;
    this.currencies = currencies;
    this.languages = languages;
    this.deploymentGroups = deploymentGroups;
    this.entity = this.setupEntityService.entity;

    if (!this.entity.isRoot()) {
      const parentEntity = this.entity.entityParent;
      if (parentEntity.isRoot()) {
        parentEntity.countries = this.countries.map(({ code }) => code);
        parentEntity.languages = this.languages.map(({ code }) => code);
        parentEntity.currencies = this.currencies.map(({ code }) => code);
      } else {
        this.entityService.getItem(this.entity.entityParent.path)
          .pipe(
            take(1),
          )
          .subscribe(
            (data: Entity) => {
              parentEntity.countries = data.countries;
              parentEntity.languages = data.languages;
              parentEntity.currencies = data.currencies;
            }
          );
      }

      if (entityJurisdictions) {
        this.entity.jurisdictions = entityJurisdictions;
      }
    }
  }

  ngOnInit(): void {
    if (!this.entity || !this.entity.entityParent) {
      return;
    }

    const patch$ = this.useJurisdictionsDataOnlyControl.valueChanges
      .pipe(
        skip(1),
        map((active) => active === 'inherited' ? null : active),
        switchMap((active) => {
          return this.entitySettingService.patchRestrictedCountries(active, this.entity.path);
        }),
      );

    combineLatest([
      this.entitySettingsService.getSettings(this.entity.path, true),
      this.entitySettingsService.getSettings(this.entity.entityParent.path),
      this.setupEntityService.settings$
    ]).pipe(
      take(1)
    ).subscribe(([settingsOwn, settingsParent, settingsInherited]) => {
      this.entitySettingsParent = settingsParent;
      this.restrictedCountries = getInheritedCountryCode(settingsOwn.restrictedCountries, settingsInherited.restrictedCountries);
      this.disableManageCountries = settingsInherited.useCountriesFromJurisdiction;
      const value = settingsOwn.useCountriesFromJurisdiction === undefined ? 'inherited' : settingsOwn.useCountriesFromJurisdiction;
      this.useJurisdictionsDataOnlyControl.setValue(value);
    });

    if (this.entity.path !== ':') {
      const parentEntity = this.entity.entityParent;
      const isNotMaster = !parentEntity.isMaster();
      const hasNoBalances = !parentEntity.hasDefinedBalances();
      const isRootParent = this.entity.entityParent.isRoot();
      if (isNotMaster && hasNoBalances && isRootParent) {
        this.entityService.getBalances(this.entity.entityParent.path, true).pipe(
          takeUntil(this.destroyed$)
        ).subscribe(balances => {
          parentEntity.balances = balances;
        });
      }
    }

    merge(patch$, this.setupEntityService.entityNeedUpdate$).pipe(
      switchMap(() => forkJoin([this.entitySettingsService.getSettings(this.entity.path, true),
      this.entitySettingsService.getSettings(this.entity.path)])),
      takeUntil(this.destroyed$),
    ).subscribe(([settingsOwn, settingsInherited]) => {
      this.restrictedCountries = getInheritedCountryCode(settingsOwn.restrictedCountries, settingsInherited.restrictedCountries);
      this.disableManageCountries = settingsInherited.useCountriesFromJurisdiction;
    });
  }

  accessToManageCountries(condition: boolean): boolean {
    if (!this.canEdit) {
      return true;
    }
    return this.disableManageCountries ? this.disableManageCountries : condition;
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  updateRestrictedCountries(val: InheritedCountryCode[]) {
    this.restrictedCountries = val;
  }
}
