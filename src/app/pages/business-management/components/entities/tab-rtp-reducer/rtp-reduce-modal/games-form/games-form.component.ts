import { <PERSON><PERSON><PERSON>ueAccessor, NG_VALIDATORS, NG_VALUE_ACCESSOR, ValidationErrors, Validator } from '@angular/forms';
import { Component, forwardRef, HostListener, Inject, Input, OnDestroy, OnInit, Optional } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';

import { TaggedItem } from '../tagged-items/tagged-items.component';
import { FormService, SW_FORM_SERVICE } from './form-service.model';
import { GameInfo } from '../../../../../../../common/typings';

function from( game: GameInfo ): TaggedItem | undefined {
  if (!game.labels.find(( { id } ) => id === game.providerCode)) {
    game.labels.push({
      id: game.providerCode,
      title: game.providerTitle,
      group: 'provider',
    });
  }
  if (game?.code && game?.features?.supportsRtpConfigurator && !!game?.features?.baseRTP) {
    return {
      id: game.code,
      title: game.title || '',
      labels: game.labels,
      currentRtp: Number(game.features.baseRTP),
      checked: false
    };
  } else if (game?.code && game?.features?.supportsRtpConfigurator && !!game?.features?.baseRTPRange) {
    const { max, min } = game.features?.baseRTPRange;
    return {
      id: game.code,
      title: game.title || '',
      labels: game.labels,
      currentRtpMax: Number(max) || 0,
      currentRtpMin: Number(min) || 0,
      checked: false
    };
  }
}

@Component({
    selector: 'sw-games-form',
    templateUrl: './games-form.component.html',
    styleUrls: ['./games-form.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => GamesFormComponent),
            multi: true
        },
        {
            provide: NG_VALIDATORS,
            useExisting: forwardRef(() => GamesFormComponent),
            multi: true
        }
    ],
    standalone: false
})
export class GamesFormComponent implements OnDestroy, OnInit, ControlValueAccessor, Validator {
  @Input() submitted = false;

  @Input() set sourceGames( games: GameInfo[] ) {
    if (games && Array.isArray(games)) {
      this._sourceGames$.next(games && Array.isArray(games) ? games : []);
    }
  }

  gameCodes: string[] = [];
  disabled = false;
  isValid = true;
  isTouched = false;
  gameCodesCounter = 0;
  checkedOnly = false;

  toggleTexts = {
    true: 'COMPONENTS.GAMES_SELECT_MANAGER.view_all',
    false: 'COMPONENTS.GAMES_SELECT_MANAGER.view_selected'
  };

  items$: Observable<TaggedItem[]>;
  totalItems$: Observable<number>;
  onChange: ( _: any ) => void = (() => {
  });

  private _sourceGames$ = new BehaviorSubject<GameInfo[]>([]);
  private readonly destroyed = new Subject<void>();

  constructor( @Optional() @Inject(SW_FORM_SERVICE) private readonly formService: FormService ) {
    this.items$ = this._sourceGames$.pipe(
      map(( games: GameInfo[] ) => {
        const selectItems: TaggedItem[] = [];
        games.forEach(( game: GameInfo ) => {
          const item = from(game);
          if (item) {
            selectItems.push(item);
          }
        });
        return selectItems;
      })
    );
    this.totalItems$ = this.items$.pipe(
      map(items => items.length)
    );
  }

  @HostListener('blur') onblur() {
    this.isTouched = true;
    this.onTouched();
  }

  onTouched: () => void = () => {
  }

  ngOnInit() {
    if (this.formService) {
      this.formService.formSubmitted$.pipe(
        takeUntil(this.destroyed)
      ).subscribe(val => {
        this.isTouched = val;
      });
    }
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();
  }

  writeValue( val: string[] ): void {
    this.gameCodes = Array.isArray(val) ? val : [];
    this.gameCodesCounter = this.gameCodes.length;
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }

  validate(): ValidationErrors | null {
    return this.isValid ? null : { invalidForm: { valid: false } };
  }

  onSelectedChange( gameCodes: any[] ) {
    this.isValid = gameCodes.length > 0;
    this.gameCodesCounter = gameCodes.length;

    if (!this.gameCodesCounter) {
      this.checkedOnly = false;
    }

    setTimeout(() => {
      this.onChange(gameCodes);
    }, 0);
  }

  toggleVisible() {
    this.checkedOnly = !this.checkedOnly;
  }
}
