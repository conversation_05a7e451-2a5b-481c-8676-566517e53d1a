import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { GameInfo } from '../../../../../../common/typings';
import { RtpDeduction, RtpGameConfiguration } from '../../../../../../common/typings/rtp-reducer';
import { TaggedItem } from './tagged-items/tagged-items.component';


export interface DialogData {
  sourceGames: GameInfo[];
  entityName: string;
  entityPath: string;
}

@Component({
    selector: 'rtp-reduce-modal',
    templateUrl: './rtp-reduce-modal.component.html',
    styleUrls: ['./rtp-reduce-modal.component.scss'],
    standalone: false
})
export class RtpReduceModalComponent implements OnInit {
  displayedColumns: string[] = ['gameName', 'gameCode', 'currentRtp', 'newRtp', 'state'];

  sourceGames: GameInfo[] = [];
  selectedGames: TaggedItem[] = [];
  rtpGamesConfigList: RtpGameConfiguration[] = [];
  rtpDeductionList: RtpDeduction[] = [];
  entityName: string = '';
  entityPath: string = '';
  submitted: boolean = false;

  gamesFormGroup: FormGroup;
  reduceRtpFormGroup: FormGroup;
  radioGroupControl = new FormControl('reduceBy');
  minRtpValue = 0.01;

  omitBnsBalance: any;
  private readonly destroyed$ = new Subject<void>();

  constructor( private fb: FormBuilder,
               private dialogRef: MatDialogRef<RtpReduceModalComponent>,
               @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.sourceGames = this.data.sourceGames;
    this.entityName = this.data.entityName;
    this.entityPath = this.data.entityPath;

    this.initForms();
  }

  get reduceRtpForm(): FormGroup {
    return this.reduceRtpFormGroup;
  }

  get gamesListControl(): FormControl {
    return this.gamesFormGroup.get('gamesList') as FormControl;
  }

  get rtpGameByControl(): FormControl {
    return this.reduceRtpFormGroup.get('rtpGameBy') as FormControl;
  }

  get rtpGameToControl(): FormControl {
    return this.reduceRtpFormGroup.get('rtpGameTo') as FormControl;
  }

  ngOnInit(): void {
    this.gamesListControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(data => {
      this.selectedGames = data;
    });

    this.radioGroupControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((value: string) => {
      this.resetRtpControls();

      if (value === 'reduceBy') {
        this.rtpGameToControl.disable();
        this.rtpGameByControl.enable();
      } else {
        this.rtpGameToControl.enable();
        this.rtpGameByControl.disable();
      }
    });

    this.rtpGameByControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((rtp: number) => {
      this.rtpDeductionList = this.rtpGameByControl.errors ?
        this.reduceNewRtpBy() :
        this.reduceNewRtpBy(rtp);
    });

    this.rtpGameToControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe((rtp: number) => {
      this.rtpDeductionList = this.rtpGameToControl.errors ?
        this.reduceNewRtpTo() :
        this.reduceNewRtpTo(rtp);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  reduceNewRtpBy(rtp: number = 0.0){
    return this.rtpGamesConfigList.reduce(( acc: RtpDeduction[], cur: RtpGameConfiguration ) => {
      if (cur.currentRtp) {
        cur.newRtpDeduction = Number.parseFloat((cur.currentRtp - rtp).toFixed(3));
      } else if (cur.currentRtpMax && cur.currentRtpMin) {
        cur.newRtpDeductionMax = Number.parseFloat((cur.currentRtpMax - rtp).toFixed(3));
        cur.newRtpDeductionMin = Number.parseFloat((cur.currentRtpMin - rtp).toFixed(3));
      }

      cur = this.rtpHasErrors(cur);

      acc.push({
        newRtpDeduction: rtp,
        gameCode: cur.gameCode
      });
      return acc;
    }, []);
  }

  reduceNewRtpTo(rtp: number = 0){
    return this.rtpGamesConfigList.reduce(( acc: RtpDeduction[], cur: RtpGameConfiguration ) => {
      if (cur.currentRtp) {
        cur.newRtpDeduction = rtp || cur.currentRtp;
      } else if (cur.currentRtpMax && cur.currentRtpMin) {
        cur.newRtpDeductionMax = rtp || cur.currentRtpMax;
        cur.newRtpDeductionMin = Number.parseFloat((rtp - (cur.currentRtpMax - cur.currentRtpMin)).toFixed(2)) || cur.currentRtpMin;
      }

      cur = this.rtpHasErrors(cur);

      const newRtpDeduction: number = cur?.currentRtp ? cur.currentRtp - rtp : cur.currentRtpMax - rtp;

      acc.push({
        newRtpDeduction: Number.parseFloat(newRtpDeduction.toFixed(2)),
        gameCode: cur.gameCode
      });
      return acc;
    }, []);
  }

  onNoClick() {
    this.dialogRef.close(null);
  }

  goNext() {
    this.rtpGamesConfigList = this.selectedGames.reduce(( acc: RtpGameConfiguration[], cur: TaggedItem ) => {
      acc.push({
        gameName: cur.title,
        gameCode: cur.id,
        currentRtp: cur.currentRtp || null,
        currentRtpMax: cur.currentRtpMax || null,
        currentRtpMin: cur.currentRtpMin || null,
      });
      return acc;
    }, []);

    this.rtpDeductionList = this.reduceNewRtpBy();
  }

  goBack() {
    this.resetRtpControls();
  }

  onConfirmClick() {
    this.reduceRtpFormGroup.markAsTouched();
    if (this.reduceRtpForm.valid) {
      this.dialogRef.close(this.rtpDeductionList);
    }
  }

  getErrorList(): string {
    const list = this.rtpGamesConfigList.filter(row => row.error);

    return list.length && list.length > 3 ?
      list.slice(0, 3).map(row => row.gameName).join(', ').concat(` and ${list.length - 3} more`) :
      list.map(row => row.gameName).join(', ');
  }

  isValidRtpForm(): boolean {
    return !this.reduceRtpForm.valid || !!this.rtpGamesConfigList.find(row => row.error);
  }

  private initForms() {
    this.gamesFormGroup = this.fb.group({
      gamesList: ['']
    });
    this.reduceRtpFormGroup = this.fb.group({
      rtpGameBy: [
        '', Validators.compose([
          Validators.required,
          Validators.min(this.minRtpValue),
          Validators.max(99.99)
        ])
      ],
      rtpGameTo: [
        { value: '', disabled: true }, Validators.compose([
          Validators.required,
          Validators.min(this.minRtpValue),
          Validators.max(99.99)
        ])
      ]
    });
  }

  private resetRtpControls() {
    this.rtpGameToControl.reset();
    this.rtpGameByControl.reset();

    this.rtpGameToControl.updateValueAndValidity();
    this.rtpGameByControl.updateValueAndValidity();
  }

  private rtpHasErrors(cur: RtpGameConfiguration) {
    cur.error = false;
    if (cur?.newRtpDeduction < this.minRtpValue) {
      cur.newRtpDeduction = cur.currentRtp || null;
      cur.error = true;
    } else if ((cur?.newRtpDeductionMax < this.minRtpValue)) {
      cur.newRtpDeductionMax = cur.currentRtpMax || null;
      cur.error = true;
    } else if ((cur?.newRtpDeductionMin < this.minRtpValue)) {
      cur.newRtpDeductionMin = cur.currentRtpMin || null;
      cur.error = true;
    }
    return cur;
  }
}
