import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges, ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { isObservable, Observable, of, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, startWith, switchMap, takeUntil, tap } from 'rxjs/operators';

export interface TaggedItem {
  checked: boolean;
  currentRtp?: number;
  currentRtpMax?: number;
  currentRtpMin?: number;
  readonly id: string;
  readonly title: string;
  readonly labels: {
    readonly group: string;
    readonly title: string;
  }[];
}

@Component({
    selector: 'sw-tagged-items',
    templateUrl: 'tagged-items.component.html',
    styleUrls: ['./tagged-items.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class TaggedItemsComponent implements OnInit, OnChanges, OnDestroy {
  @ViewChild(CdkVirtualScrollViewport, { static: false }) virtualScroll?: CdkVirtualScrollViewport;
  @Input() items: TaggedItem[] | Observable<TaggedItem[]> = [];
  @Input() selectedItems: string[] = [];
  @Input() disabled = false;
  @Input() height = '500px';
  @Input() checkedOnly = false;

  @Output() changed = new EventEmitter<TaggedItem[]>();

  searchInput = new FormControl('');
  loading = false;
  availableItems: TaggedItem[] = [];

  private cachedItems: TaggedItem[] = [];
  private readonly inputChanged$ = new Subject();
  private readonly destroyed$ = new Subject();

  constructor( private readonly cd: ChangeDetectorRef ) {
    this.inputChanged$.pipe(
      switchMap(() => isObservable(this.items) ? this.items : of(this.items)),
      filter(items => !!items),
      tap(() => {
        this.loading = true;
      }),
      map(items => items.map(item => ({
        ...item,
        checked: this.selectedItems.indexOf(item.id) !== -1
      }))),
      startWith([]),
      tap(() => {
        this.loading = false;
      }),
      takeUntil(this.destroyed$)
    ).subscribe(items => {
      this.cd.markForCheck();
      this.cachedItems = items;
      this.setSearchTerm(this.searchInput.value);
    });
  }

  ngOnInit() {
    if (this.searchInput) {
      this.searchInput.valueChanges.pipe(
        debounceTime(100),
        distinctUntilChanged(),
        takeUntil(this.destroyed$)
      ).subscribe(search => {
        this.setSearchTerm(search);
      });
    }
  }

  ngOnChanges( { selectedItems, items }: SimpleChanges ): void {
    if (items || selectedItems) {
      this.inputChanged$.next();
    } else {
      this.setSearchTerm(this.searchInput.value);
    }
  }

  ngAfterViewInit() {
    if (!this.virtualScroll) {
      return;
    }

    const observer = new IntersectionObserver(( entries ) => {
      entries.forEach(( { isIntersecting } ) => {
        if (isIntersecting && this.virtualScroll) {
          this.virtualScroll.checkViewportSize();
        }
      });
    });

    observer.observe(this.virtualScroll.elementRef.nativeElement);
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  trackByFn = ( _: number, { id }: TaggedItem ) => id;

  get isAllChecked(): boolean {
    return this.availableItems.every(( { checked } ) => checked);
  }

  toggleAllChecked() {
    const allChecked = !this.isAllChecked;
    this.availableItems.forEach(game => {
      game.checked = allChecked;
    });
    this.cd.markForCheck();
    this.emitOnChanged();
  }

  emitOnChanged() {
    this.changed.emit(this.cachedItems.filter(( { checked } ) => checked).map(( id: TaggedItem ) => id));
    this.setSearchTerm(this.searchInput.value);
  }

  getLabelClass( group: string ): string {
    switch (group) {
      case 'platform':
        return 'sw-bg-purple';
      case 'class':
        return 'sw-bg-deep-orange';
      case 'feature':
        return 'sw-bg-red';
      default:
        return 'sw-bg-light-blue';
    }
  }

  clearSearch() {
    this.searchInput.setValue('');
  }

  private setSearchTerm( search: string ) {
    const needle = search.toLowerCase();
    this.availableItems = this.cachedItems.filter(( { id, title, labels, checked } ) => {
      return (this.checkedOnly ? checked : true)
        && (title.toLowerCase().indexOf(needle) > -1
          || id.toLowerCase().indexOf(needle) > -1
          || labels.map(label => label.title.toLowerCase()).filter(text => text.indexOf(needle) > -1).length > 0);
    });
    this.cd.markForCheck();
  }
}
