import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  DEFAULT_PAGE_SIZE, RowAction, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiNotificationsService,
  SwuiSelectOption, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';

import { BoConfirmationComponent } from '../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { Entity } from '../../../../../common/models/entity.model';
import { GameService } from '../../../../../common/services/game.service';
import { RtpReducerService } from '../../../../../common/services/rtp-reducer.service';
import { GameInfo } from '../../../../../common/typings';
import { RtpDeduction, RtpReducer } from '../../../../../common/typings/rtp-reducer';
import { SetupEntityService } from '../setup-entity.service';
import { RtpReduceModalComponent } from './rtp-reduce-modal/rtp-reduce-modal.component';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';


@Component({
    selector: 'tab-rtp-reducer',
    templateUrl: './tab-rtp-reducer.component.html',
    styleUrls: ['./tab-rtp-reducer.component.scss'],
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: RtpReducerService },
    ],
    standalone: false
})
export class TabRtpReducerComponent implements OnInit, OnDestroy {
  readonly entity?: Entity;
  readonly entityPath?: string;
  readonly entityName: string;
  readonly actions: RowAction[];

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<RtpReducer>;

  schema: SwuiGridField[] = SCHEMA_LIST;
  filterSchema: SwuiGridField[] = SCHEMA_FILTER;
  items: RtpReducer[] = [];
  games: GameInfo[] = [];
  pageSize = DEFAULT_PAGE_SIZE;

  private readonly destroyed$ = new Subject<void>();

  constructor( { entity }: SetupEntityService,
               private service: RtpReducerService,
               private dialog: MatDialog,
               private gameService: GameService,
               private translation: TranslateService,
               private notifications: SwuiNotificationsService,
  ) {
    this.entity = entity;
    this.entityPath = this.entity?.path;
    this.entityName = this.entity?.name || '';
    this.actions = this.setActions();

    this.gameService.getAllGames(this.entityPath, false, true)
      .pipe(
        tap(( data: GameInfo[] ) => {
          this.games = data;
        }),
        takeUntil(this.destroyed$))
      .subscribe(( games: GameInfo[] ) => {
        this.filterSchema = this.filterSchema.map(( item ) => {
          if (item.field === 'gameCode__in' && item.type === 'select') {
            item.data = games.map<SwuiSelectOption>(( { code, title } ) => ({ id: code, text: `${title} (${code})` }));
          }
          return item;
        });
      });
  }

  ngOnInit(): void {
    this.grid.dataSource.requestData = {
      path: this.entityPath,
    };
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  createNewConfiguration() {
    this.dialog.open(RtpReduceModalComponent, {
      width: '1000px',
      data: {
        sourceGames: this.games,
        entityName: this.entityName,
        entityPath: this.entityPath
      },
      maxHeight: '95vh',
      disableClose: true
    }).afterClosed().pipe(
      filter(data => !!data),
      switchMap(( rtpDeductionList: RtpDeduction[] ) => this.service.changeGamesRtp(this.entityPath, rtpDeductionList)),
      switchMap(() => this.translation.get(('ENTITY_SETUP.RTP_REDUCER.updatedRtd'))),
      tap(( message ) => this.notifications.success(message, '')),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.grid.dataSource.loadData();
    });
  }

  private setActions(): RowAction[] {
    return [
      new RowAction({
        title: 'Reset RTP',
        inMenu: true,
        fn: ( row: RtpReducer ) => this.removeRtp(row.entityTitle, row.gameCode)
      }),
    ];
  }

  private removeRtp( entityTitle: string, gameCode: string ) {
    const body: RtpDeduction[] = [
      {
        gameCode,
        newRtpDeduction: 0
      }
    ];

    this.dialog.open(BoConfirmationComponent, {
      width: '500px',
      data: {
        message: this.translation.instant('ENTITY_SETUP.RTP_REDUCER.MODAL.resetMessage',
          { title: entityTitle })
      },
      disableClose: true
    }).afterClosed().pipe(
      filter(( isConfirm: boolean ) => isConfirm),
      switchMap(() => this.service.changeGamesRtp(this.entityPath, body)),
      switchMap(() => this.translation.get(('ENTITY_SETUP.RTP_REDUCER.removerRtd'))),
      tap(( message ) => this.notifications.success(message, '')),
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.grid.dataSource.loadData();
    });
  }
}
