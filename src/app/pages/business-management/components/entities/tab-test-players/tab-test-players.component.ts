import { Component } from '@angular/core';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { SetupEntityService } from '../setup-entity.service';

@Component({
    selector: 'tab-test-players',
    templateUrl: './tab-test-players.component.html',
    standalone: false
})
export class TabTestPlayersComponent {
  readonly entity?: Entity;
  readonly entitySettings?: EntitySettingsModel;

  total?: number;

  constructor( { entity, settings }: SetupEntityService ) {
    this.entity = entity;
    this.entitySettings = settings;
  }

  handleTotalChanged( value: number ) {
    this.total = value;
  }
}
