import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  RowAction, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiNotificationsService, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';
import { iif, Subject, throwError } from 'rxjs';
import { catchError, filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { TestPlayersService } from '../../../../../common/services/test-players.service';
import { TestPlayer } from '../../../../../common/typings/test-player';
import { SCHEMA_FILTER, SCHEMA_LIST } from './schema';
import { TestPlayersDialogComponent } from './test-players-dialog/test-players-dialog.component';

@Component({
    selector: 'test-players-merchant',
    templateUrl: './test-players-merchant.component.html',
    providers: [
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: TestPlayersService },
    ],
    standalone: false
})
export class TestPlayersMerchantComponent implements OnInit, OnDestroy {

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<TestPlayer>;

  @Input()
  set entity( value: Entity ) {
    if (!value) {
      return;
    }

    this._entity = value;
    this.useEntityPathInGridRequests();
  }

  get entity(): Entity {
    return this._entity;
  }

  @Input() public entitySettings: EntitySettingsModel;

  @Output() public totalChanged: EventEmitter<number> = new EventEmitter();

  schema: SwuiGridField[] = SCHEMA_LIST;
  filterSchema: SwuiGridField[] = SCHEMA_FILTER;

  public rowActions: RowAction[] = [];
  public total: number;

  private _entity: Entity;
  private destroyed$ = new Subject<void>();

  constructor( private dialog: MatDialog,
               private testPlayersService: TestPlayersService,
               private translate: TranslateService,
               private notifications: SwuiNotificationsService,
  ) {
    this.setRowActions();
  }

  ngOnInit() {
    this.grid?.dataSource?.total$
      .pipe(
        takeUntil(this.destroyed$),
      )
      .subscribe(( total: number ) => {
        if (total) {
          this.total = total;
          this.totalChanged.emit(this.total);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  setRowActions() {
    this.rowActions = [
      new RowAction({
        title: 'ENTITY_SETUP.TEST_PLAYERS.GRID.deleteTestPlayer',
        icon: 'delete',
        inMenu: true,
        fn: ( testPlayer: TestPlayer ) => this.showDeleteTestPlayerModal(testPlayer),
      }),
    ];
  }

  showTestPlayersModal( testPlayer?: TestPlayer ) {
    let codes: string[] = this.grid?.dataSource?.data?.map(( testPl: TestPlayer ) => testPl.code.toLowerCase());

    const dialogRef = this.dialog.open(TestPlayersDialogComponent, {
      width: '600px',
      disableClose: true,
      data: {
        testPlayer: testPlayer,
        codes: codes,
      }
    });

    dialogRef.afterClosed()
      .pipe(
        filter(data => !!data),
        switchMap(( data: TestPlayer ) => {
          const path = this.entity ? this.entity.path : null;

          return iif(() => !!testPlayer,
            this.testPlayersService.editTestPlayer({ code: testPlayer?.code, endDate: data.endDate }, path),
            this.testPlayersService.createTestPlayer(data, path));
        }),
        switchMap(( value?: TestPlayer ) => {
          return !!testPlayer ?
            this.translate.get('ENTITY_SETUP.TEST_PLAYERS.notificationEdited', { code: value?.code }) :
            this.translate.get('ENTITY_SETUP.TEST_PLAYERS.notificationCreated');
        }),
        tap(( message: string ) => this.notifications.success(message)),
        catchError(err => {
          this.notifications.error(err?.error?.message);

          return throwError(err);
        }),
        takeUntil(this.destroyed$),
      )
      .subscribe(() => {
        this.grid.dataSource.loadData();
      });
  }

  showDeleteTestPlayerModal( testPlayer: TestPlayer ) {
    this.dialog.open(BoConfirmationComponent, {
      width: '600px',
      data: { message: this.translate.instant('ENTITY_SETUP.TEST_PLAYERS.deleteTestPlayerConfirmation', { playerCode: testPlayer.code }) },
      disableClose: true
    }).afterClosed()
      .pipe(
        filter(( isConfirmed: boolean ) => isConfirmed),
        switchMap(() => this.testPlayersService.deleteTestPlayer(this.entity.path, testPlayer.code)),
        catchError(err => {
          this.notifications.error(err?.error?.message);

          return throwError(err);
        }),
      )
      .subscribe(
        () => {
          this.notifications.success(
            this.translate.instant('ENTITY_SETUP.TEST_PLAYERS.testPlayerSuccessfullyDeleted', { playerCode: testPlayer.code }),
            ''
          );
          this.grid.dataSource.loadData();
        },
      );
  }

  private useEntityPathInGridRequests() {
    if (this.grid) {
      this.grid.dataSource.requestData = {
        path: this.entity.path
      };
    }
  }
}
