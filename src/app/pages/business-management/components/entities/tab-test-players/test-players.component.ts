import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { of, Subject, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../../common/components/bo-confirmation/bo-confirmation.component';
import { ErrorMessage } from '../../../../../common/components/mat-user-editor/user-form.component';
import { EntitySettingsModel } from '../../../../../common/models/entity-settings.model';
import { Entity } from '../../../../../common/models/entity.model';
import { EntitySettingsService } from '../../../../../common/services/entity-settings.service';
import { ValidationService } from '../../../../../common/services/validation.service';

export const DEFAULT_MAX_TEST_PLAYERS = 10;

@Component({
    selector: 'test-players',
    templateUrl: './test-players.component.html',
    styleUrls: ['./test-players.component.scss'],
    standalone: false
})
export class TestPlayersComponent implements OnInit {

  messageErrors: ErrorMessage = {
    required: 'VALIDATION.required',
    invalidDigitsOnly: 'VALIDATION.positiveInteger',
    greaterThan: 'VALIDATION.greaterThan',
  };

  @Input() public entity: Entity;
  @Input() public entitySettings: EntitySettingsModel;
  @Input() public total?: number;

  public form: FormGroup;

  private destroyed$ = new Subject<void>();

  constructor(
    private entitySettingsService: EntitySettingsService<EntitySettingsModel>,
    private fb: FormBuilder,
    private translate: TranslateService,
    private notifications: SwuiNotificationsService,
    private dialog: MatDialog,
  ) {
  }

  ngOnInit() {
    this.initForm();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  get autoCreateTestJackpotControl(): FormControl | undefined {
    return this.form?.get('autoCreateTestJackpot') as FormControl;
  }

  get maxTestPlayersControl(): FormControl | undefined {
    return this.form?.get('maxTestPlayers') as FormControl;
  }

  initForm() {
    this.form = this.fb.group({
      autoCreateTestJackpot: false,
      maxTestPlayers: [
        DEFAULT_MAX_TEST_PLAYERS, Validators.compose([
          Validators.required,
          ValidationService.digitsOnlyValidator,
          ValidationService.greaterThanValidator(0),
        ])
      ],
    });

    this.form.patchValue(this.entitySettings);

    this.handleAutoCreateJackpotToggleChange();
  }

  isMaxTestPlayersSmallerThanTotal(): boolean {
    return this.entity.isMerchant && this.maxTestPlayersControl.value && this.maxTestPlayersControl.valid && this.total &&
      this.maxTestPlayersControl.value < this.total;
  }

  isApplyButtonDisabled(): boolean {
    return this.entitySettings.maxTestPlayers === this.maxTestPlayersControl.value || this.maxTestPlayersControl.invalid ||
      this.isMaxTestPlayersSmallerThanTotal();
  }

  handleAutoCreateJackpotToggleChange() {
    this.autoCreateTestJackpotControl.valueChanges
      .pipe(
        switchMap(( value: boolean ) => {
          const settings = { autoCreateTestJackpot: value };

          return this.entitySettingsService.patchSettings(settings, this.entity.path);
        }),
        switchMap(( data: EntitySettingsModel ) => {
          const message = data.autoCreateTestJackpot ?
            'ENTITY_SETUP.TEST_PLAYERS.autoCreateTestJackpotStrategyWasSetSuccessfully' :
            'ENTITY_SETUP.TEST_PLAYERS.autoCreateTestJackpotStrategyWasUnsetSuccessfully';

          return this.translate.get(message);
        })
      )
      .subscribe(message => {
        this.notifications.success(message, '');
        this.entitySettings.autoCreateTestJackpot = this.autoCreateTestJackpotControl.value;
      });
  }

  showConfirmation( event: Event ) {
    event.preventDefault();

    if (this.maxTestPlayersControl.valid) {
      this.dialog.open(BoConfirmationComponent, {
        width: '600px',
        data: { message: 'ENTITY_SETUP.TEST_PLAYERS.changeMaxTestPlayersConfirmation' },
        disableClose: true
      }).afterClosed()
        .pipe(
          switchMap(( isConfirmed: boolean ) => {
            if (isConfirmed) {
              const settings = { maxTestPlayers: this.maxTestPlayersControl.value };

              return this.entitySettingsService.patchSettings(settings, this.entity.path);
            } else {
              this.maxTestPlayersControl.patchValue(this.entitySettings.maxTestPlayers || DEFAULT_MAX_TEST_PLAYERS);

              return of(null);
            }
          }),
          switchMap(( result ) => {
            return result ?
              this.translate.get('ENTITY_SETUP.TEST_PLAYERS.maxTestPlayersEditSuccess') :
              of(null);
          }),
          catchError(err => {
            this.notifications.error(err.error.message);

            this.maxTestPlayersControl.patchValue(this.entitySettings.maxTestPlayers || DEFAULT_MAX_TEST_PLAYERS);

            return throwError(err);
          }),
        )
        .subscribe(
          ( message: string ) => {
            if (message) {
              this.notifications.success(message, '');
              this.entitySettings.maxTestPlayers = this.maxTestPlayersControl.value;
            }
          }
        );
    }
  }
}
