import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { User } from '../../../../../users/user.model';

export interface DeleteUserDialogData {
  user: User;
}

@Component({
    selector: 'delete-user-dialog',
    templateUrl: 'delete-user-dialog.component.html',
    standalone: false
})
export class DeleteUserDialogComponent implements OnInit {
  constructor(
    public dialogRef: MatDialogRef<DeleteUserDialogComponent, DeleteUserDialogData>,
    @Inject(MAT_DIALOG_DATA) public data: DeleteUserDialogData,
  ) {
  }

  ngOnInit() {
  }

  confirmDelete() {
    this.dialogRef.close(this.data);
  }
}
