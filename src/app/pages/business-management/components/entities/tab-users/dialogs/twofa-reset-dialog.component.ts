import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { User } from '../../../../../users/user.model';
import { Entity } from '../../../../../../common/models/entity.model';
import { getTwofaType, TwofaTypeItem } from '../../../../../auth/two-factor/two-factor.model';

export interface TwofaResetDialogData {
  user: User;
  entity: Entity;
}

export interface TwofaResetDialogResult {
  user: User;
  types: string[];
}

@Component({
    selector: 'twofa-reset-dialog',
    templateUrl: 'twofa-reset-dialog.component.html',
    standalone: false
})
export class TwofaResetDialogComponent implements OnInit {

  types: TwofaTypeItem[];
  selected: Set<TwofaTypeItem> = new Set<TwofaTypeItem>();

  constructor(
    public dialogRef: MatDialogRef<TwofaResetDialogComponent, TwofaResetDialogResult>,
    @Inject(MAT_DIALOG_DATA) public data: TwofaResetDialogData,
  ) {
    this.initAuthTypes();
  }

  ngOnInit() {
  }

  confirmReset() {
    this.dialogRef.close({
      user: this.data.user,
      types: [...this.selected].map(type => type.name)
    });
  }

  toggleSelectType(type: TwofaTypeItem) {
    if (this.selected.has(type)) {
      this.selected.delete(type);
    } else {
      this.selected.add(type);
    }
  }

  private initAuthTypes() {
    if (this.data.user && !this.data.user.hasOwnProperty('twoFAInfo')) return;

    const typeNames = this.data.user.twoFAInfo && this.data.user.twoFAInfo.authTypes
      ? this.data.user.twoFAInfo.authTypes
      : [this.data.user.twoFAInfo.defaultAuthType];

    this.types = typeNames.map(type => getTwofaType(type));
  }
}
