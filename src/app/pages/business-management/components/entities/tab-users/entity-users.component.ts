import { Component, Input, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import {
  ActionModel, SwHubAuthService, SwuiGridComponent, SwuiGridDataService, SwuiGridField, SwuiNotificationsService, SwuiTopFilterDataService
} from '@skywind-group/lib-swui';

import { Subject } from 'rxjs';
import { filter, finalize, switchMap, take } from 'rxjs/operators';
import { CsvService } from 'src/app/common/services/csv.service';
import {
  UserEditorDialogComponent, UserEditorDialogData
} from '../../../../../common/components/mat-user-editor/user-editor-dialog.component';
import { Entity } from '../../../../../common/models/entity.model';
import { RoleService } from '../../../../../common/services/role.service';
import { UserActionsService } from '../../../../../common/services/user-actions.service';
import { UserService } from '../../../../../common/services/user.service';
import { User } from '../../../../users/user.model';
import { DeleteUserDialogComponent } from './dialogs/delete-user-dialog.component';
import { TwofaResetDialogComponent, TwofaResetDialogResult } from './dialogs/twofa-reset-dialog.component';
import { UnblockUserDialogComponent } from './dialogs/unblock-user-dialog.component';


import { SCHEMA_LIST } from './users.schema';

@Component({
    selector: 'entity-users',
    templateUrl: './entity-users.component.html',
    providers: [
        CsvService,
        SwuiTopFilterDataService,
        { provide: SwuiGridDataService, useExisting: UserService },
    ],
    standalone: false
})
export class EntityUsersComponent {
  @Input()
  set entity( value: Entity ) {
    if (!value) return;
    this._entity = value;
    this.useEntityPathInGridRequests();
  }

  get entity(): Entity {
    return this._entity;
  }

  @Input() brief?: Entity;

  @ViewChild(SwuiGridComponent, { static: true }) grid: SwuiGridComponent<User>;

  readonly schemaItems: SwuiGridField[] = SCHEMA_LIST;
  actions: ActionModel[];

  private _entity: Entity;
  private readonly destroyed$ = new Subject<void>();

  constructor( public service: UserService<User>,
               public roleService: RoleService,
               public notifications: SwuiNotificationsService,
               public translate: TranslateService,
               public authService: SwHubAuthService,
               private dialog: MatDialog,
               private userActionsService: UserActionsService,
  ) {
  }

  ngOnInit() {
    this.setActions();
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  showResetTwoAuthDialog( user: User ) {
    this.fixUserPath(user);

    const dialogRef = this.dialog.open(TwofaResetDialogComponent, {
      width: '600px',
      data: {
        entity: this.entity,
        user: user
      },
      disableClose: true
    });

    dialogRef.afterClosed().pipe(
      filter(( data ) => !!data),
      switchMap(( data: TwofaResetDialogResult ) => this.userActionsService.onTwofaResetAction(data)),
      switchMap(() => this.translate.get('ENTITY_SETUP.USERS.twofaResetSuccessfull', user)),
    ).subscribe(( message ) => {
      this.notifications.success(message);
      this.grid.dataSource.loadData();
    });
  }

  showUnblockUserConfirmDialog( user: User ) {
    this.fixUserPath(user);

    this.service.getUserProfile(user)
      .pipe(take(1))
      .subscribe(( userProfile ) => {
        const dialogRef = this.dialog.open(UnblockUserDialogComponent, {
          width: '700px',
          data: { user: { ...user, ...userProfile } },
          disableClose: true
        });

        dialogRef.afterClosed()
          .pipe(
            filter(( data ) => !!data),
            switchMap(( data ) => this.userActionsService.onUnblockUserAction(data, this.entity)),
            switchMap(() => this.translate.get('ENTITY_SETUP.USERS.userUnblocked', user))
          )
          .subscribe(( message ) => {
            this.notifications.success(message);
            this.grid.dataSource.loadData();
          });
      });
  }

  showUserEditorModal( user = new User() ) {
    this.dialog.open<any, UserEditorDialogData>(UserEditorDialogComponent, {
      width: '700px',
      data: {
        user,
        entity: this.entity,
        brief: this.brief
      },
      disableClose: true
    }).afterClosed()
      .pipe(take(1))
      .subscribe(() => {
        this.grid.dataSource.loadData();
      });
  }

  onWidgetActionFn( { field, row, payload } ) {
    switch (field) {
      case 'status':
        const user: User = Object.assign({}, row, { status: payload.status });
        this.fixUserPath(user);

        this.service.updateItem(user)
          .pipe(take(1))
          .subscribe(( response ) => {
            if ('status' in response) {
              row.status = response.status;
            }
            payload.onCompleteFn();
          });
        break;

      default:
        break;
    }
  }

  showDeleteConfirmDialog( user ) {
    this.fixUserPath(user);
    const dialogRef = this.dialog.open(DeleteUserDialogComponent, {
      width: '600px',
      data: { user },
      disableClose: true
    });

    dialogRef.afterClosed()
      .pipe(
        filter(( data ) => !!data),
        switchMap(() => this.userActionsService.onDeleteUserAction(user, this.entity)),
        switchMap(() => this.translate.get('ENTITY_SETUP.USERS.userRemoved', user)),
        take(1),
        finalize(() => this.grid.dataSource.loadData())
      )
      .subscribe(
        ( message ) => this.notifications.success(message),
        ( error ) => this.notifications.error(error.statusText, `Status: ${error.status}`)
      );
  }

  private setActions() {
    this.actions = [
      new ActionModel({
        icon: 'edit',
        fn: this.showUserEditorModal.bind(this)
      }),
    ];

    if (this.userActionsService.unblockUserGranted(this.entity)) {
      this.actions.push(new ActionModel({
        title: 'ENTITY_SETUP.USERS.MODALS.unblockUserModalTitle',
        icon: 'lock_open',
        fn: this.showUnblockUserConfirmDialog.bind(this),
        canActivateFn: (row: User) => {
          return row.status === 'locked_by_auth';
        }
      }));
    }

    if (this.userActionsService.twofaResetEnabled()) {
      this.actions.push(new ActionModel({
        title: 'ENTITY_SETUP.USERS.reset2faAction',
        icon: 'security',
        fn: this.showResetTwoAuthDialog.bind(this),
        canActivateFn: ( row: User ) => !!row.twoFAInfo
          && (('defaultAuthType' in row.twoFAInfo) || ('authTypes' in row.twoFAInfo && row.twoFAInfo.authTypes.length))
      }));
    }

    if (this.userActionsService.deleteUserGranted(this.entity)) {
      this.actions.push(new ActionModel({
        title: 'ENTITY_SETUP.USERS.deleteUserAction',
        icon: 'delete',
        fn: this.showDeleteConfirmDialog.bind(this),
        canActivateFn: ( row: User ) => row.username !== this.authService.username
      }));
    }

    if (this.actions.length > 1) {
      Object.assign(this.actions[0], {
        title: 'ENTITY_SETUP.USERS.editUserAction'
      });
    }
  }

  private useEntityPathInGridRequests() {
    this.grid.dataSource.requestData = {
      path: this.entity.path
    };
  }

  private fixUserPath( user: User ) {
    const pathsNotEquals = user.entity !== this.entity.path;
    const userPathIncorrect = user.entity.indexOf(this.entity.path) === -1;
    if (userPathIncorrect && pathsNotEquals) {
      user.entity = this.entity.path + user.entity; // hotfix for user path
    }
  }
}
