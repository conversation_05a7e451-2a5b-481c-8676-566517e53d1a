import { Component } from '@angular/core';

import { Entity } from '../../../../../common/models/entity.model';
import { SetupEntityService } from '../setup-entity.service';
import { ActivatedRoute } from '@angular/router';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { PERMISSIONS_LIST } from '../../../../../app.constants';


@Component({
    selector: 'tab-users',
    templateUrl: './tab-users.component.html',
    standalone: false
})
export class TabUsersComponent {
  readonly entity?: Entity;
  readonly brief?: Entity;
  public readonly allowedView: boolean;

  constructor(
    { entity }: SetupEntityService,
    { snapshot: { data: { brief } } }: ActivatedRoute,
    authService: SwHubAuthService
  ) {
    this.brief = brief;
    this.entity = entity;
    this.allowedView = authService.allowedTo(PERMISSIONS_LIST.USER_VIEW);
  }
}
