import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
    selector: 'entity-linker',
    templateUrl: './entity-linker.component.html',
    styleUrls: ['./entity-linker.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class EntityLinkerComponent implements OnInit {
  @Input() links: string[] = [];
  @Output() lickClick = new EventEmitter<string>();

  ngOnInit(): void {
  }

  onLinkClick( link: string ) {
    this.lickClick.emit(link);
  }
}
