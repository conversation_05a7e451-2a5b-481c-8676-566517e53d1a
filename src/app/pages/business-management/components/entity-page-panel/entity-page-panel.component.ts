import { Component, Input } from '@angular/core';
import { PanelAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Entity } from '../../../../common/models/entity.model';

function copyToClipboard(str: string) {
  const el = document.createElement('textarea');
  el.value = str;
  el.setAttribute('readonly', '');
  el.style.position = 'absolute';
  el.style.left = '-9999px';
  document.body.appendChild(el);
  el.select();
  document.execCommand('copy');
  document.body.removeChild(el);
}

const styles = {
  height: '26px',
  fontSize: '14px',
  backgroundColor: '#D9DBDF',
  lineHeight: '14px',
  fontWeight: '400',
  textTransform: 'initial',
  padding: '0 10px',
  color: '#2A2C44',
  marginTop: '9px',
  minWidth: 'auto',
  letterSpacing: 'initial'
};

@Component({
    selector: 'entity-page-panel',
    templateUrl: 'entity-page-panel.component.html',
    standalone: false
})
export class EntityPagePanelComponent {
  @Input()
  set entity(val: Entity | undefined) {
    this._entity = val;
    this.panelActions = this.getPanelActions(this._entity);
  }

  get entity(): Entity | undefined {
    return this._entity;
  }

  @Input() pageTitle: string;

  panelActions: PanelAction[] = [];
  private _entity?: Entity;

  constructor(private notification: SwuiNotificationsService) {
  }

  getTitle(): string {
    return this.pageTitle ? this.pageTitle : 'Edit ' + (this.entity?.title || this.entity?.name);
  }

  private getPanelActions(entity: Entity): PanelAction[] {
    return [
      {
        title: entity?.name || '',
        fontSet: 'material-icons-outline',
        icon: 'info',
        hover: 'Copy entity code',
        actionFn: () => {
          if (entity?.name) {
            copyToClipboard(entity?.name);
            this.notification.success('Entity name was copied to clipboard');
          }
        },
        getStyle: () => styles
      },
      {
        title: entity?.key || '',
        fontSet: 'material-icons-outline',
        icon: 'vpn_key',
        hover: 'Copy entity key',
        actionFn: () => {
          if (entity?.key) {
            copyToClipboard(entity?.key);
            this.notification.success('Entity key was copied to clipboard');
          }
        },
        getStyle: () => styles
      }
    ];
  }
}
