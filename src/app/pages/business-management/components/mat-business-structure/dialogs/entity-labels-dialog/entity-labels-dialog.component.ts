import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { PERMISSIONS_NAMES, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { OptionModel } from '@skywind-group/lib-swui/swui-autoselect/option.model';
import { Observable } from 'rxjs';
import { groupBy, map, mergeMap, take, toArray } from 'rxjs/operators';
import { Entity } from '../../../../../../common/models/entity.model';
import { SelectOptionModel } from '../../../../../../common/models/select-option.model';
import { EntityLabelsService } from '../../../../../../common/services/entity-labels.service';
import { LabelsService } from '../../../../../../common/services/labels.service';
import { Label } from '../../../../../../common/typings/label';

@Component({
    selector: 'entity-labels-dialog',
    templateUrl: './entity-labels-dialog.component.html',
    styleUrls: ['./entity-labels-dialog.component.scss'],
    standalone: false
})
export class EntityLabelsDialogComponent implements OnInit {

  entity: Entity;
  labelGroupsSelectOptions: SelectOptionModel[];

  form: FormGroup = new FormGroup({});

  labelsSelectOptions: SelectOptionModel[][] = [];

  isLabelsInReadMode = false;

  constructor( @Inject(MAT_DIALOG_DATA) public data: {
                 entity: Entity,
                 labelGroupsSelectOptions: SelectOptionModel[],
               },
               public dialogRef: MatDialogRef<EntityLabelsDialogComponent>,
               private fb: FormBuilder,
               private labelsService: LabelsService,
               private entityLabelsService: EntityLabelsService,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               private readonly authService: SwHubAuthService,
  ) {
    this.entity = this.data.entity;
    this.labelGroupsSelectOptions = this.data.labelGroupsSelectOptions;

    this.checkIsLabelsInReadMode();
  }

  ngOnInit() {
    this.entityLabelsService.getEntityLabels(this.entity.path)
      .pipe(
        take(1),
        mergeMap(res => res),
        groupBy(( label: Label ) => label.group.id),
        mergeMap(groups => groups.pipe(
          toArray(),
          map(items => {
            return { labelGroup: groups.key, labels: items.map(item => item.id) };
          }),
        )),
        toArray(),
      )
      .subscribe(
        formValue => {
          this.initForm(formValue);
        }
      );
  }

  checkIsLabelsInReadMode() {
    this.isLabelsInReadMode = this.authService.allowedTo(
      [
        PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_VIEW,
        PERMISSIONS_NAMES.ENTITYLABELS_VIEW,
      ]
    ) && !this.authService.allowedTo(
      [
        PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS,
        PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_CREATE,
        PERMISSIONS_NAMES.ENTITYLABELS,
        PERMISSIONS_NAMES.ENTITYLABELS_CREATE,
      ]
    );
  }

  addLabel( index: number ): ( label: string ) => Observable<any> {
    return ( label: string ) => {
      let groupId = this.itemsArray?.controls[index]?.value?.labelGroup;

      return this.entityLabelsService.addLabel({ groupId: groupId, title: label })
        .pipe(map(item => {
          let selectOption = this.toOptionModel(item);
          this.labelsSelectOptions[index] = [...this.labelsSelectOptions[index], selectOption];

          return selectOption;
        }));
    };
  }

  mapFn( id: string ) {
    return { id };
  }

  initForm( formValue ) {
    this.form = this.fb.group({
      items: this.fb.array([]),
    });

    formValue.forEach(
      value => {
        this.addLabelsGroup(value);
      }
    );
  }

  get itemsArray(): FormArray {
    return this.form?.get('items') as FormArray;
  }

  addLabelsGroup( value?: any ) {
    this.labelsService.getEntityLabels()
      .pipe(
        take(1),
      )
      .subscribe(
        ( entityLabels: Label[] ) => {
          this.itemsArray.push(this.initRowFormGroup(value, entityLabels));
        }
      );
  }

  initRowFormGroup( rowValue?: any, entityLabels?: Label[] ): FormGroup {
    let form = this.fb.group({
      labelGroup: [{ value: '', disabled: this.isLabelsInReadMode }],
      labels: { value: '', disabled: this.isLabelsInReadMode || !rowValue },
    });

    form.get('labelGroup').valueChanges.subscribe(
      value => {
        this.labelGroupsSelectOptions.forEach(labelGroup => {
          if (labelGroup.id === value) {
            labelGroup.disabled = true;
          }
        });

        this.labelsSelectOptions.push(
          entityLabels?.filter(label => label.groupId === value)?.map(( label: Label ) => {
            return { id: label.id, text: label.title };
          }));

        if (!this.isLabelsInReadMode) {
          form.get('labels').enable();
        }
      }
    );

    if (rowValue) {
      form.patchValue(rowValue);
    }

    return form;
  }

  getLabelsGroupTitle( id ): string {
    return this.labelGroupsSelectOptions.find(item => item.id === id)?.text;
  }

  removeLabelsGroup( index ) {
    let labelsGroup = this.itemsArray?.controls[index]?.value?.labelGroup;
    this.labelGroupsSelectOptions.forEach(labelGroup => {
      if (labelGroup.id === labelsGroup) {
        labelGroup.disabled = false;
      }
    });

    this.itemsArray.removeAt(index);
    this.labelsSelectOptions.splice(index, 1);
  }

  save() {
    let value = this.form?.value?.items?.map(row => row.labels);
    if (Array.isArray(value) && value.length) {
      value = value.reduce(( res, i ) => res?.concat(i))?.filter(item => item);
    }

    this.entityLabelsService.updateEntityLabels(this.entity.path, Array.isArray(value) ? value : []).subscribe(
      () => {
        this.notifications.success(this.translate.instant('ENTITY_SETUP.LABELS.updated'));
        this.dialogRef.close();
      }
    );
  }

  private toOptionModel( item: any ): OptionModel {
    return {
      id: item.id,
      text: item.title,
    };
  }
}
