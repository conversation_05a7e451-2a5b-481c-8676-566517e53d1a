import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PERMISSIONS_NAMES, SwHubAuthService } from '@skywind-group/lib-swui';

import { forkJoin, Subject } from 'rxjs';
import { finalize, take, takeUntil, tap } from 'rxjs/operators';
import { codeArrayToObjectReducer, EntityService } from '../../../../../../common/services/entity.service';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { Currency } from '../../../../../../common/typings';
import { BusinessStructureService } from '../../business-structure.service';

import { StructureEntityModel } from '../../structure-entity.model';

@Component({
    selector: 'balance-form',
    templateUrl: './form.component.html',
    styleUrls: [
        './manage-balance.component.scss',
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class FormComponent {
  @Input() set entity( val ) {
    if (!val) return;
    this._entity = val;
  }

  get entity(): StructureEntityModel {
    return this._entity;
  }

  @Input() allowZero = false;

  @Output() formSubmitted: EventEmitter<any> = new EventEmitter();
  @Output() formInvalidSubmitted: EventEmitter<boolean> = new EventEmitter();

  deposit = 'deposit';
  withdrawal = 'withdrawal';

  availableBalance: number;
  parents: StructureEntityModel[];
  transactionDirection: string; // default value
  transactionDirectionList: string[];
  form: FormGroup;
  submitted: boolean = false;
  _selectedCurrencyCode: string;
  selectedCurrencyLabel: string;

  private _entity: StructureEntityModel;
  private currenciesHash: { [code: string]: Currency };
  private _currencies: Currency[] = [];
  private parentsHash: { [path: string]: StructureEntityModel };
  private readonly destroyed$ = new Subject<void>();

  /**
   * Used in case when we're adding balance via entity-setup tab by clicking to link-like balance
   * @param {string} code
   */
  @Input()
  set selectedCurrencyCode( code: string ) {
    if (!code) return;

    this._selectedCurrencyCode = code;
  }

  @Input()
  set currencies( currencies: Currency[] ) {
    if (!currencies || !currencies.length) return;

    this._currencies = currencies;
    this.currenciesHash = this._currencies.reduce(codeArrayToObjectReducer, {});
  }

  get currencies(): Currency[] {
    return this._currencies;
  }

  get selectedCurrency(): string {
    return this.form.get('currency').value;
  }

  constructor(
    private fb: FormBuilder,
    private entityService: EntityService<StructureEntityModel>,
    private readonly cdr: ChangeDetectorRef,
    private readonly bsService: BusinessStructureService,
    readonly authService: SwHubAuthService
  ) {

    const transactionDirectionList = [];

    if (authService.areGranted([PERMISSIONS_NAMES.FINANCE_CREDIT])) {
      transactionDirectionList.push(this.deposit);
    }

    if (authService.areGranted([PERMISSIONS_NAMES.FINANCE_DEBIT])) {
      transactionDirectionList.push(this.withdrawal);
    }

    this.transactionDirectionList = transactionDirectionList;
    this.transactionDirection = this.transactionDirectionList[0];
  }

  ngOnInit() {
    this.attachParents();
    this.initForm();
    this.selectedCurrencyLabel = this.getSelectedCurrencyLabel(this.selectedCurrency);
    this.refreshAvailableBalance();
    this.form.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.formInvalidSubmitted.emit(this.form.invalid);
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  getBalance( code: string ): number {
    return this.entity.balances.hasOwnProperty(code) ? this.entity.balances[code].main : 0;
  }

  getCurrency( code: string ): Currency {
    if (!this.currenciesHash) return;
    return this.currenciesHash[code];
  }

  getSelectedCurrencyLabel( code: string ): string {
    return this.entity.balances.hasOwnProperty(code) ? this.entity.balances[code].label : code;
  }

  isInvalidForm() {
    return this.form.invalid;
  }

  applyChanges() {
    this.submitted = true;
    this.form.markAllAsTouched();
    this.cdr.markForCheck();
    this.formInvalidSubmitted.emit(this.form.invalid);

    if (this.form.valid) {
      const rawValue = this.form.getRawValue();

      this.formSubmitted.emit({
        transactionDirection: this.transactionDirection,
        currency: rawValue.currency,
        balance: rawValue.balance,
        entities: this.getEntitiesChain(),
        activeParentPath: rawValue.activeParentPath,
      });
    }
  }

  previewTotal( enteredBalance: number ): number {
    let code = this.selectedCurrency;
    const diff = this.isWithdrawal() ? -1 : 1;
    return this.getBalance(code) + enteredBalance * diff;
  }

  selectFirstAvailableCurrency() {
    let valueNotSet: boolean = !this.form.get('currency').value && this.form.get('currency').value !== 0;
    if (!this.form.get('currency').disabled && valueNotSet) {
      this.form.get('currency').setValue(this.entity.currencies[0]);
    }
  }

  private initForm() {
    this.form = this.fb.group({
      activeParentPath: [this.entity.entityParent.path, Validators.required],
      currency: ['', Validators.required],
      transactionDirection: [this.transactionDirection, Validators.required],
      balance: [null, Validators.required],
    });

    this.subscribeToChanges();
    this.checkForSelectedCurrency();
    this.selectFirstAvailableCurrency();
  }

  private attachParents() {
    this.parents = this.bsService.getAllParents(this.entity) as StructureEntityModel[];
    this.parentsHash = this.parents.reduce(( parents, parent ) => {
      return { ...parents, [parent.path]: parent };
    }, {});

    this.loadParentBalances();
  }

  private refreshAvailableBalance() {
    const code = this.form.get('currency').value;
    const path = this.form.get('activeParentPath').value;
    if (code === '' || path === '') return;
    this.availableBalance = this.parentsHash[path]?.getBalance(code);
  }

  private subscribeToChanges() {
    const currencyFormControl = this.form.get('currency');
    const balanceFormControl = this.form.get('balance');
    const activeParentPathControl = this.form.get('activeParentPath');
    const directionControl = this.form.get('transactionDirection');

    currencyFormControl.valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( currency ) => {
        if (currency) {
          this.refreshAvailableBalance();
          this.updateBalanceValidators();
          this.selectedCurrencyLabel = this.getSelectedCurrencyLabel(this.selectedCurrency);
        }
        balanceFormControl.updateValueAndValidity();
      });

    activeParentPathControl.valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( path ) => {
        if (path) {
          this.refreshAvailableBalance();
          this.updateBalanceValidators();
        }
        balanceFormControl.updateValueAndValidity();
      });

    directionControl.valueChanges.pipe(takeUntil(this.destroyed$))
      .subscribe(( direction ) => {
        this.transactionDirection = direction;
        this.updateBalanceValidators();
        balanceFormControl.updateValueAndValidity();
      });

    balanceFormControl.valueChanges.pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.updateBalanceValidators();
      });
  }

  private updateBalanceValidators() {
    const balanceFormControl = this.form.get('balance');
    const currencyFormControl = this.form.get('currency');
    const balance = this.getBalance(currencyFormControl.value);
    balanceFormControl.valueChanges.pipe(
      tap(() => this.refreshAvailableBalance()),
      take(1),
      finalize(() => {
        balanceFormControl.updateValueAndValidity({ emitEvent: false });
        this.submitted = false;
      })
    ).subscribe(() => {
      const maxValue = this.isWithdrawal() ? balance : this.availableBalance;
      const validators = [
        Validators.min(0),
        Validators.max(maxValue)
      ];

      const zeroValidator = this.allowZero ? [] : [ValidationService.notEquals(0), ValidationService.notEquals(null)];

      balanceFormControl.setValidators(Validators.compose([...validators, ...zeroValidator]));
    });
  }

  private loadParentBalances() {
    const request = this.parents.map(( parent ) => this.entityService.getBalances(parent.path));

    forkJoin([
      ...request
    ]).pipe(
      take(1)
    ).subscribe(( balance ) => {
      this.parents.map(( parent: StructureEntityModel, index ) => {
        parent.balances = balance[index];
      });
      this.refreshAvailableBalance();
      this.cdr.detectChanges();
    });
  }

  private getEntitiesChain() {
    const chain: StructureEntityModel[] = [this.entity];
    const boundPath = this.form.get('activeParentPath').value;
    const parents = this.bsService.getAllParents(this.entity, false) as StructureEntityModel[]; // oldest will be last element

    let boundReached: boolean = false;
    let i = 0;

    while (!boundReached) {
      boundReached = parents[i].path === boundPath;

      if (!boundReached) {
        chain.push(parents[i++]);
      }
    }

    return chain;
  }

  private checkForSelectedCurrency() {
    if (this._selectedCurrencyCode) {
      this.form.get('currency').setValue(this._selectedCurrencyCode);
    }
  }

  private isWithdrawal(): boolean {
    return this.transactionDirection === this.withdrawal;
  }
}
