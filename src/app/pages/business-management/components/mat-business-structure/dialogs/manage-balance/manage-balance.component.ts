import { ChangeDetectorRef, Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Observable } from 'rxjs';
import { finalize, map, mergeMap } from 'rxjs/operators';
import { Entity } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { Currency } from '../../../../../../common/typings';
import { StructureEntityModel } from '../../structure-entity.model';
import { multiplyAmount } from '../../../../../../common/core/currecy-transform';

export interface MatBalanceDialogData {
  entity: StructureEntityModel;
  currencies: Currency[];
  selectedCurrencyCode?: string;
}

@Component({
    selector: 'manage-balance',
    templateUrl: 'manage-balance.component.html',
    styleUrls: [
        './manage-balance.component.scss',
    ],
    standalone: false
})
export class ManageBalanceComponent implements OnInit {
  entity: Entity;
  selectedCurrencyCode: string;
  currencies: Currency[] = [];
  loading: boolean;

  @Output() public balanceAdded: EventEmitter<any> = new EventEmitter();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MatBalanceDialogData,
    public dialogRef: MatDialogRef<ManageBalanceComponent>,
    private entityService: EntityService<Entity>,
    private notifications: SwuiNotificationsService,
    private cdr: ChangeDetectorRef,
    private translate: TranslateService,
  ) {
    this.entity = data.entity;
    this.currencies = data.currencies;
    if ('selectedCurrencyCode' in data) {
      this.selectedCurrencyCode = data.selectedCurrencyCode;
    }
  }

  ngOnInit(): void {
  }

  onFormSubmit( { currency, balance, entities, activeParentPath, transactionDirection } ) {
    const withdrawal = transactionDirection === 'withdrawal';
    let message: string = '';

    if (withdrawal) {
      message = 'ENTITY_SETUP.REGIONAL.MODALS.notificationBalanceSubtracted';
    } else {
      message = 'ENTITY_SETUP.REGIONAL.MODALS.notificationBalanceAdded';
      entities = entities.reverse();
    }

    const amount = multiplyAmount(balance, currency);

    this.loading = true;
    const sub = this.chainMergeRequests([...<Entity[]>entities], currency, amount, transactionDirection)
      .pipe(
        finalize(() => {
          this.loading = false;
          sub.unsubscribe();
        })
      )
      .subscribe(
        ( updated: Entity ) => {
          this.entity.update(updated);
          this.translate.get(message, { amount: balance, currency: currency, entity: this.entity.name })
            .subscribe(msg => this.notifications.success(msg, ''));
          this.balanceAdded.emit({ currency, balance, activeParentPath });
        },
        () => {
          /**
           * @TODO: if user received error "Insufficient funds", then we need to refresh parent balances
           * and to give him second chance with updated limits
           */
        },
        () => {
          this.cdr.markForCheck();
          // sub.unsubscribe();
          this.dialogRef.close();
        }
      );
  }


  private chainMergeRequests( entities: Entity[], currency, balance, transactionDirection: string ): Observable<Entity> {
    const withdrawal = transactionDirection.toLowerCase() === 'withdrawal';
    const buildRequest = ( { path }, currencyCode, balanceValue ) => {
      return withdrawal
        ? this.entityService.debitEntity(path, currencyCode, balanceValue)
        : this.entityService.creditEntity(path, currencyCode, balanceValue);
    };

    return entities.reduce(( merged$, entity ) => {
      // other requests except first one
      return merged$.pipe(
        mergeMap(( prev ) => {
          return buildRequest(entity, currency, balance).pipe(
            map(next => {
              return withdrawal ? prev : next;
            })
          );
        })
      );
    }, buildRequest(entities.shift(), currency, balance)); // initial request
  }
}
