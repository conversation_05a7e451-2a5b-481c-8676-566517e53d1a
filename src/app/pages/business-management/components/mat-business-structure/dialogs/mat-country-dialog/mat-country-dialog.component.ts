import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Entity } from '../../../../../../common/models/entity.model';
import { Country } from '../../../../../../common/typings';

export interface MatCountryDialogData {
  entity: Entity;
  countriesType: 'allowed' | 'restricted' | 'blocked';
  items: CountryItem[];
}

export class CountryItem {
  code: string;
  displayName: string;
  selected?: boolean;
  disabled?: boolean;

  constructor(data: Country) {
    this.code = data.code || '';
    this.displayName = data.displayName || '';
  }
}

@Component({
    selector: 'mat-country-dialog',
    templateUrl: './mat-country-dialog.component.html',
    styleUrls: ['./mat-country-dialog.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class MatCountryDialogComponent implements OnInit {

  entity: Entity;
  dataSource: MatTableDataSource<CountryItem>;
  countriesType: 'allowed' | 'restricted' | 'blocked';

  nameFilter: string;
  displayedColumns: string[] = ['name', 'code'];
  allSelected = false;
  submitted = false;

  get selectedItems(): CountryItem[] {
    return this.dataSource.data.filter(item => item.selected);
  }

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MatCountryDialogData,
    public dialogRef: MatDialogRef<MatCountryDialogComponent>,
  ) {
    this.entity = this.data.entity;
    this.countriesType = this.data.countriesType;
    this.dataSource = new MatTableDataSource(this.data.items);
  }

  ngOnInit(): void {
    if (this.countriesType === 'restricted') {
      this.allSelected = this.dataSource.filteredData
        .filter((item) => item.code !== this.entity.defaultCountry)
        .every(item => item.selected);
    }
  }

  applyChanges() {
    this.dialogRef.close({
      entity: this.entity,
      selected: this.selectedItems.map(item => item.code)
    });
  }

  applyFilter(filterValue: string) {
    this.nameFilter = filterValue;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.allSelected = this.dataSource.filteredData.every(item => item.selected);
  }

  allSelectedChanged(changeEvent: MatCheckboxChange) {
    this.dataSource.data.map(item => item.selected = false);

    if (changeEvent.checked) {
      this.dataSource.filteredData.map(item => {
        item.selected = !(item.code === this.entity.defaultCountry && this.countriesType === 'restricted');
      });
    }
  }
}
