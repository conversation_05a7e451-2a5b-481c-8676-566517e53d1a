import { Component, Inject, OnInit } from '@angular/core';
import { Currency } from '../../../../../../common/typings';
import { Entity } from '../../../../../../common/models/entity.model';
import { codeArrayToObjectReducer } from '../../../../../../common/services/entity.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatTableDataSource } from '@angular/material/table';


export interface MatCurrencyDialogData {
  entity: Entity;
  currencies: Currency[];
}

export class CurrencyItem implements Currency {
  code: string;
  displayName: string;
  label?: string;
  isSocial?: true;
  selected?: boolean;

  constructor( data: Currency ) {
    this.code = data.code || '';
    this.displayName = data.displayName || '';
    this.label = data.label;
    this.isSocial = data.isSocial;
  }
}

@Component({
    selector: 'mat-currency-dialog',
    templateUrl: 'mat-currency-dialog.component.html',
    styleUrls: [
        './mat-currency-dialog.component.scss'
    ],
    standalone: false
})

export class MatCurrencyDialogComponent implements OnInit {

  entity: Entity;
  currencies: Currency[];
  loading: boolean;

  nameFilter: string;
  displayedColumns: string[] = ['name', 'code'];
  dataSource: MatTableDataSource<CurrencyItem>;
  allSelected = false;

  private hash: { [code: string]: Currency };
  private defaultCurrency: CurrencyItem;

  get selectedItems(): CurrencyItem[] {
    return this.dataSource.data.filter(item => item.selected);
  }

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: MatCurrencyDialogData,
    public dialogRef: MatDialogRef<MatCurrencyDialogComponent>,
  ) {
    this.currencies = data.currencies;
    this.entity = data.entity;
  }

  ngOnInit() {
    this.buildHash();
    this.initAvailableCurrencies();
  }

  applyFilter( filterValue: string ) {
    this.nameFilter = filterValue;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.allSelected = this.dataSource.filteredData.every(item => item.selected);
  }

  applyChanges() {
    this.dialogRef.close({
      entity: this.entity,
      selected: this.selectedItems.map(item => item.code)
    });
  }

  allSelectedChanged( changeEvent: MatCheckboxChange ) {
    this.dataSource.data.map(item => item.selected = false);

    if (changeEvent.checked) {
      this.dataSource.filteredData.map(item => item.selected = true);
    } else {
      this.defaultCurrency.selected = true;
    }
  }

  get availableCurrencyCodes(): string[] {
    let currencyCodes = this.entity.entityParent.currencies;
    const rootParent = this.entity.entityParent.isRoot();
    const rootCurrenciesMismatch = currencyCodes.length !== this.currencies.length;

    if (rootParent && rootCurrenciesMismatch) {
      currencyCodes = Object.keys(this.hash);
    }

    return currencyCodes;
  }

  private initAvailableCurrencies() {
    const items = this.availableCurrencyCodes
      .map(( code ) => {
        const item = new CurrencyItem(this.hash[code]);
        item.selected = this.entity.currencies.indexOf(code) > -1;

        if (code === this.entity.defaultCurrency) {
          this.defaultCurrency = item;
        }

        return item;
      });
    this.dataSource = new MatTableDataSource(items);
  }

  private buildHash() {
    this.hash = this.currencies.reduce(codeArrayToObjectReducer, {});
  }
}
