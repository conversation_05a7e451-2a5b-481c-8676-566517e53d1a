import { Component, Inject, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { PERMISSIONS_NAMES, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { forkJoin, Observable, of, Subject } from 'rxjs';
import { filter, finalize, map, mergeMap, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { Entity } from 'src/app/common/models/entity.model';
import { CountryService } from 'src/app/common/services/country.service';
import { ErrorMessage } from '../../../../../../common/components/mat-user-editor/user-form.component';
import { CurrencyService } from '../../../../../../common/services/currency.service';
import { EntityService } from '../../../../../../common/services/entity.service';
import { JurisdictionService } from '../../../../../../common/services/jurisdiction.service';
import { LanguagesService } from '../../../../../../common/services/languages.service';
import { Country, Currency, Language } from '../../../../../../common/typings';
import { Jurisdiction } from '../../../../../../common/typings/jurisdiction';
import { FormComponent } from '../manage-balance/form.component';
import { RegionalItem } from './mat-regional-item/mat-regional-item.component';
import { multiplyAmount } from '../../../../../../common/core/currecy-transform';


@Component({
    selector: 'mat-regional-dialog',
    templateUrl: './mat-regional-dialog.component.html',
    styleUrls: ['./mat-regional-dialog.component.scss'],
    standalone: false
})
export class MatRegionalDialogComponent {
  messageErrors: ErrorMessage = {
    emptyList: 'VALIDATION.required',
  };

  availableCurrencyCodes: Currency[] = [];
  availableLanguageCodes: RegionalItem[] = [];
  availableCountryCodes: Country[] = [];
  availableJurisdictions: RegionalItem[] = [];

  selectedLanguageCodes: string[] = [];
  selectedCountryCodes: string[] = [];
  selectedJurisdictionCodes: string[] = [];

  onFormChanged: boolean = false;

  form: FormGroup;
  selectedIndex = 0;
  formValue = [];
  isCurrenciesAvailable = true;
  isJurisdictionsAvailable = true;

  @ViewChild(FormComponent) set balanceForm( balanceForm: FormComponent ) {
    this._balanceForm = balanceForm;
  }

  get entity(): Entity {
    return this._entity;
  }

  set entity( val: Entity ) {
    if (!val) return;
    this._entity = val;
  }

  private _balanceForm: FormComponent;

  private readonly tabs: ({ [key: string]: number }) = {
    currency: 0,
    country: 1,
    language: 2,
    jurisdictions: 3,
    entityJurisdiction: 4
  };
  private _entity: Entity;
  private readonly destroyed$ = new Subject<void>();
  private balanceFormInvalid = false;

  constructor( @Inject(MAT_DIALOG_DATA) public dialogEntity: any,
               readonly authService: SwHubAuthService,
               private dialogRef: MatDialogRef<MatRegionalDialogComponent>,
               private fb: FormBuilder,
               private readonly currencyService: CurrencyService<Currency>,
               private readonly languageService: LanguagesService<Language>,
               private readonly countryService: CountryService<Country>,
               private readonly jurisdictionService: JurisdictionService,
               private readonly entityService: EntityService<Entity>,
               private readonly notifications: SwuiNotificationsService,
               private readonly translate: TranslateService
  ) {
    this.isCurrenciesAvailable = authService.areGranted([PERMISSIONS_NAMES.ENTITY_BALANCE])
      && authService.areGranted([PERMISSIONS_NAMES.FINANCE_CREDIT, PERMISSIONS_NAMES.FINANCE_DEBIT]);
    this.isJurisdictionsAvailable = authService.areGranted([PERMISSIONS_NAMES.KEYENTITY_JURISDICTION_VIEW])
      && authService.areGranted([PERMISSIONS_NAMES.JURISDICTION_VIEW]);
    this.entity = new Entity(dialogEntity);
    this.initForm();
    this.selectedIndex = this.tabs[dialogEntity.activeTab];

    const jurisdictions$ = this.isJurisdictionsAvailable
      ? [
        this.jurisdictionService.getEntityJurisdictions(dialogEntity.entityParentPath),
        this.jurisdictionService.getEntityJurisdictions(dialogEntity.path)
      ]
      : [of([]), of([])];

    forkJoin([
      this.currencyService.getList('', dialogEntity.entityParentPath),
      this.countryService.getList('', dialogEntity.entityParentPath),
      this.languageService.getList('', dialogEntity.entityParentPath),
      ...jurisdictions$
    ]).pipe(
      take(1),
      finalize(() => this.setFormValue())
    ).subscribe(( regionalItems ) => {
      this.availableCurrencyCodes = regionalItems[this.tabs['currency']] as RegionalItem[];
      this.availableCountryCodes = regionalItems[this.tabs['country']] as RegionalItem[];
      this.availableLanguageCodes = regionalItems[this.tabs['language']] as RegionalItem[];
      this.availableJurisdictions = regionalItems[3].reduce(( acc: RegionalItem[], cur: Jurisdiction ) => {
        acc.push({
          displayName: cur.title,
          code: cur.code
        });
        return acc;
      }, []);

      this.selectedJurisdictionCodes = this.entity.jurisdictions = regionalItems[4].map(( jur: Jurisdiction ) => jur.code);

      this.selectedCountryCodes = this.entity.countries;
      this.selectedLanguageCodes = this.entity.languages;
    });
  }

  ngOnInit() {
    this.form.valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        const valueResult = [];
        Object.keys(this.form.controls).map(( key ) => {
          const parsedValue = {
            [key]: this.form.get(key).value,
            changed: this.form.get(key).dirty
          };
          if (parsedValue.changed) {
            this.onFormChanged = parsedValue.changed;
          }
          valueResult.push(parsedValue);
        });
        this.formValue = valueResult;
      });

    this.form.get('jurisdictions').valueChanges.subscribe(( data ) => {
      if (!data.length && this.isJurisdictionsAvailable) {
        this.form.get('jurisdictions').setErrors({ emptyList: !data.length }, { emitEvent: false });
      }
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  applyChanges() {
    this.formValue = this.formValue.reduce(( acc, cur ) => {
      Object.keys(this.form.controls).forEach(key => {
        if (cur[key]) {
          acc.push({
            [key]: cur[key]
          });
        }
      });
      return acc;
    }, []);

    Object.assign(this.entity, ...this.formValue);
  }

  onInvalidSubmitForm( invalid: boolean ) {
    this.balanceFormInvalid = invalid;
  }

  getError( control: AbstractControl ): string {
    return this.messageErrors[Object.keys(control.errors)[0]];
  }

  onApply() {
    if (this._balanceForm) {
      this._balanceForm.formSubmitted
        .pipe(take(1))
        .subscribe(val => {
          this.onFormSubmit(val);
        });

      this._balanceForm.applyChanges();

      return;
    }

    this.onFormSubmit();
  }

  onFormSubmit( val? ) {
    const { currency, balance, entities, transactionDirection } = val || {} as any;
    const deleteRequest = [of(null)];
    const addRequest = [of(null)];
    const updateEntityRequest = [of(null)];
    const balanceRequest = [of(null)];
    this.applyChanges();

    if (JSON.stringify(this.selectedJurisdictionCodes) !== JSON.stringify(this.entity.jurisdictions) && this.isJurisdictionsAvailable) {
      const deleted = this.findDifference(this.selectedJurisdictionCodes, this.entity.jurisdictions);
      const added = this.findDifference(this.entity.jurisdictions, this.selectedJurisdictionCodes);

      if (deleted.length && this.entity.isReseller()) {
        deleted.forEach(( item: string ) => {
          deleteRequest.push(this.jurisdictionService.deleteEntityJurisdiction(this.entity.path, item));
        });
      }
      if (added.length) {
        added.forEach(( item: string ) => {
          addRequest.push(this.jurisdictionService.addEntityJurisdiction(this.entity.path, item));
        });
      }
    }

    if ((JSON.stringify(this.selectedLanguageCodes) !== JSON.stringify(this.entity.languages)) ||
      (JSON.stringify(this.selectedCountryCodes) !== JSON.stringify(this.entity.countries))) {
      updateEntityRequest.push(this.entityService.updateEntityItem(this.entity));
    }

    if (val && !this.balanceFormInvalid && balance !== 0 && balance !== null) {
      const amount = multiplyAmount(balance, currency);
      balanceRequest.push(this.chainMergeRequests([...<Entity[]>entities], currency, amount, transactionDirection));
    }

    if (this.form.valid) {
      forkJoin(deleteRequest).pipe(
        switchMap(() => forkJoin([...addRequest, ...updateEntityRequest, ...balanceRequest])),
        filter(data => !!data),
        tap(() => this.notifications.success(this.translate.instant('ENTITY_SETUP.REGIONAL.regionalsWereAdded'))),
        takeUntil(this.destroyed$),
        finalize(() => this.dialogRef.close())
      ).subscribe();
    } else {
      const failedTab = Object.keys(this.form.controls).filter(key => this.form.get(key).errors);
      this.selectedIndex = this.tabs[failedTab[0]];
    }
  }

  onSelectedIndexChange( index: number ) {
    this.selectedIndex = index;
  }

  private chainMergeRequests( entities: Entity[], currency, balance, transactionDirection: string ): Observable<Entity> {
    const withdrawal = transactionDirection.toLowerCase() === 'withdrawal';
    const buildRequest = ( { path }, currencyCode, balanceValue ) => {
      return withdrawal
        ? this.entityService.debitEntity(path, currencyCode, balanceValue)
        : this.entityService.creditEntity(path, currencyCode, balanceValue);
    };

    if (!withdrawal) {
      entities.reverse();
    }

    return entities.reduce(( merged$, entity ) => {
      return merged$.pipe(
        mergeMap(( prev ) => {
          return buildRequest(entity, currency, balance).pipe(
            map(next => {
              return withdrawal ? prev : next;
            })
          );
        })
      );
    }, buildRequest(entities.shift(), currency, balance));
  }

  private setFormValue() {
    Object.keys(this.form.controls).forEach(key => {
      this.form.get(key).patchValue(this.entity[key]);
    });
  }

  private initForm() {
    this.form = this.fb.group({
      currencies: [],
      countries: [],
      languages: [],
      jurisdictions: []
    });
  }

  private findDifference( before: string[], after: string[] ) {
    return before.filter(( e ) => {
      return !(after.includes(e));
    });
  }


}
