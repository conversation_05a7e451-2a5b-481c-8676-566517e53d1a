import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatTableDataSource } from '@angular/material/table';
import { BehaviorSubject, combineLatest, ReplaySubject, Subject } from 'rxjs';
import { filter, finalize, map, takeUntil } from 'rxjs/operators';
import { EntityType } from '../../../../../../../common/models/entity.model';

export interface RegionalItem {
  code: string;
  name?: string;
  displayName: string;
  selected?: boolean;
}

export class Regional implements RegionalItem {
  code: string;
  name?: string;
  displayName: string;
  selected?: boolean;

  constructor(data: RegionalItem) {
    this.code = data.code || '';
    this.displayName = data.displayName || data.name || '';
  }
}

@Component({
    selector: 'mat-regional-item',
    templateUrl: './mat-regional-item.component.html',
    styleUrls: ['./mat-regional-item.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => MatRegionalItemComponent),
            multi: true
        },
    ],
    standalone: false
})
export class MatRegionalItemComponent implements OnInit, ControlValueAccessor {
  @Input() entityDefaultItem: string;
  @Input() entityType: EntityType = 'entity';
  @Input() regionalTabName: string;

  @Input()
  set availableItemCodes(val: RegionalItem[]) {
    if (!val?.length) return;
    this.availableItemCodes$.next(val);
  }

  readonly toggleTexts = {
    true: 'View all',
    false: 'View selected'
  };

  nameFilter: string;
  displayedColumns: string[] = ['name', 'code'];

  loading: boolean = true;
  allSelected: boolean = false;
  selectedOnly: boolean = false;
  disabledSelectAll: boolean = false;

  dataSource: MatTableDataSource<RegionalItem>;
  entityItems = new BehaviorSubject<string[]>([]);
  selectedOnly$ = new ReplaySubject<RegionalItem[]>(1);
  availableItemCodes$ = new BehaviorSubject<RegionalItem[]>([]);
  availableItems: RegionalItem[] = [];

  private _onChange: ( _: any ) => void = (() => {
  });
  private defaultItem: RegionalItem | null;
  private destroyed$ = new Subject<void>();

  constructor() {
  }

  writeValue(value: string[]): void {
    this.entityItems.next(value);
  }

  registerOnChange(fn: (_: any) => void): void {
    this._onChange = fn;
  }

  registerOnTouched(): void {
  }

  ngOnInit() {
    this.disabledSelectAll = this.regionalTabName === 'jurisdictions' && this.entityType !== 'entity';

    combineLatest([this.entityItems, this.availableItemCodes$])
      .pipe(
        filter(([entityItems, items]) => !!entityItems && !!items),
        map(([entityItems, items]) => {
          return items.map(item => {
            const regionalItem = new Regional(item);
            regionalItem.selected = entityItems.indexOf(regionalItem.code) > -1;

            if (regionalItem.code === this.entityDefaultItem) {
              this.defaultItem = regionalItem;
            }

            return regionalItem;
          });
        }),
        takeUntil(this.destroyed$),
        finalize(() => this.loading = false)
      ).subscribe((items: RegionalItem[]) => {
      this.availableItems = items;
      this.dataSource = new MatTableDataSource(items);
    });

    this.selectedOnly$
      .pipe(takeUntil(this.destroyed$))
      .subscribe(data => this.dataSource.data = data);
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  applyFilter(filterValue: string) {
    this.nameFilter = filterValue;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.allSelected = this.dataSource.filteredData.every(item => item.selected);
  }

  selectedOnlyToggle(): void {
    this.selectedOnly = !this.selectedOnly;
    this.allSelected = this.selectedOnly;
    if (this.selectedOnly) {
      this.selectedOnly$.next(this.selectedItems);
    } else {
      this.selectedOnly$.next(this.availableItems);
    }
  }

  get selectedItems(): RegionalItem[] {
    return this.dataSource?.data.filter(item => item.selected);
  }

  allSelectedChanged(changeEvent: MatCheckboxChange) {
    this.dataSource.data.map(item => item.selected = false);

    if (changeEvent.checked) {
      this.dataSource.filteredData.map(item => item.selected = true);
    }
    if (this.defaultItem) {
      this.defaultItem.selected = !!this.defaultItem;
    }
    this._onChange(this.selectedItemCodes());
  }

  handleChange(event: MatCheckboxChange) {
    if (this.disabledSelectAll) {
      this.dataSource.data
        .filter(item => item.code !== event.source.value)
        .forEach(item => item.selected = false);
    }
    this._onChange(this.selectedItemCodes());
  }

  selectedItemCodes(): string[] {
    return this.selectedItems.map(item => item.code);
  }
}

